{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Input.js"], "sourcesContent": ["import React, { forwardRef } from 'react';\n\nconst Input = forwardRef(({ \n  type = 'text',\n  label,\n  placeholder,\n  error,\n  helperText,\n  disabled = false,\n  required = false,\n  icon,\n  className = '',\n  containerClassName = '',\n  ...props \n}, ref) => {\n  const baseClasses = 'block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm transition-colors';\n  \n  const stateClasses = {\n    default: 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',\n    error: 'border-red-300 focus:ring-red-500 focus:border-red-500',\n    disabled: 'bg-gray-50 border-gray-200 text-gray-500 cursor-not-allowed',\n  };\n\n  const getStateClass = () => {\n    if (disabled) return stateClasses.disabled;\n    if (error) return stateClasses.error;\n    return stateClasses.default;\n  };\n\n  const inputClasses = `\n    ${baseClasses}\n    ${getStateClass()}\n    ${icon ? 'pl-10' : ''}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <div className={containerClassName}>\n      {label && (\n        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        {icon && (\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <span className=\"text-gray-400 sm:text-sm\">\n              {icon}\n            </span>\n          </div>\n        )}\n        \n        <input\n          ref={ref}\n          type={type}\n          className={inputClasses}\n          placeholder={placeholder}\n          disabled={disabled}\n          {...props}\n        />\n      </div>\n      \n      {error && (\n        <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n      )}\n    </div>\n  );\n});\n\nInput.displayName = 'Input';\n\n// Location Icon Component\nconst LocationIcon = () => (\n  <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n  </svg>\n);\n\n// Calendar Icon Component\nconst CalendarIcon = () => (\n  <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n  </svg>\n);\n\n// Search Icon Component\nconst SearchIcon = () => (\n  <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n  </svg>\n);\n\n// Predefined Input Components based on design\nexport const DefaultInput = (props) => (\n  <Input placeholder=\"Enter text...\" {...props} />\n);\n\nexport const RequiredInput = (props) => (\n  <Input \n    label=\"Required Input\"\n    placeholder=\"This field is required\"\n    required\n    {...props} \n  />\n);\n\nexport const LocationInput = ({ label = \"From\", placeholder = \"City or Airport\", ...props }) => (\n  <Input\n    label={label}\n    placeholder={placeholder}\n    icon={<LocationIcon />}\n    {...props}\n  />\n);\n\nexport const DateInput = ({ label = \"Departure Date\", ...props }) => (\n  <Input\n    type=\"date\"\n    label={label}\n    placeholder=\"dd-mm-yyyy\"\n    icon={<CalendarIcon />}\n    {...props}\n  />\n);\n\nexport const SearchInput = (props) => (\n  <Input\n    placeholder=\"Search flights...\"\n    icon={<SearchIcon />}\n    {...props}\n  />\n);\n\nexport const DisabledInput = (props) => (\n  <Input\n    label=\"Disabled Input\"\n    placeholder=\"Cannot edit\"\n    disabled\n    {...props}\n  />\n);\n\nexport const ErrorInput = (props) => (\n  <Input\n    label=\"Input with Error\"\n    placeholder=\"Enter email\"\n    error=\"Please enter a valid email address\"\n    {...props}\n  />\n);\n\nexport const HelperTextInput = (props) => (\n  <Input\n    label=\"Input with Helper Text\"\n    placeholder=\"Enter password\"\n    helperText=\"Must be at least 8 characters\"\n    type=\"password\"\n    {...props}\n  />\n);\n\n// Flight booking specific inputs\nexport const FromInput = (props) => (\n  <LocationInput label=\"From\" placeholder=\"City or Airport\" {...props} />\n);\n\nexport const ToInput = (props) => (\n  <LocationInput label=\"To\" placeholder=\"City or Airport\" {...props} />\n);\n\nexport const DepartureDateInput = (props) => (\n  <DateInput label=\"Departure Date\" {...props} />\n);\n\nexport const ReturnDateInput = (props) => (\n  <DateInput label=\"Return Date\" {...props} />\n);\n\nexport default Input;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,sBAAQ,IAAA,uNAAU,EAAC,QAYtB;QAZuB,EACxB,OAAO,MAAM,EACb,KAAK,EACL,WAAW,EACX,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,IAAI,EACJ,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,eAAe;QACnB,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,gBAAgB;QACpB,IAAI,UAAU,OAAO,aAAa,QAAQ;QAC1C,IAAI,OAAO,OAAO,aAAa,KAAK;QACpC,OAAO,aAAa,OAAO;IAC7B;IAEA,MAAM,eAAe,AAAC,SAElB,OADA,aAAY,UAEZ,OADA,iBAAgB,UAEhB,OADA,OAAO,UAAU,IAAG,UACV,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QAAI,WAAW;;YACb,uBACC,yOAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,yOAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,yOAAC;gBAAI,WAAU;;oBACZ,sBACC,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;kCAKP,yOAAC;wBACC,KAAK;wBACL,MAAM;wBACN,WAAW;wBACX,aAAa;wBACb,UAAU;wBACT,GAAG,KAAK;;;;;;;;;;;;YAIZ,uBACC,yOAAC;gBAAE,WAAU;0BAA6B;;;;;;YAG3C,cAAc,CAAC,uBACd,yOAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;KAvEM;AAyEN,MAAM,WAAW,GAAG;AAEpB,0BAA0B;AAC1B,MAAM,eAAe,kBACnB,yOAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;;0BACjE,yOAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;0BACrE,yOAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;;MAHnE;AAON,0BAA0B;AAC1B,MAAM,eAAe,kBACnB,yOAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,yOAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;MAFnE;AAMN,wBAAwB;AACxB,MAAM,aAAa,kBACjB,yOAAC;QAAI,WAAU;QAAU,MAAK;QAAO,QAAO;QAAe,SAAQ;kBACjE,cAAA,yOAAC;YAAK,eAAc;YAAQ,gBAAe;YAAQ,aAAa;YAAG,GAAE;;;;;;;;;;;MAFnE;AAOC,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,aAAY;QAAiB,GAAG,KAAK;;;;;;MADjC;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QACC,OAAM;QACN,aAAY;QACZ,QAAQ;QACP,GAAG,KAAK;;;;;;MALA;AASN,MAAM,gBAAgB;QAAC,EAAE,QAAQ,MAAM,EAAE,cAAc,iBAAiB,EAAE,GAAG,OAAO;yBACzF,yOAAC;QACC,OAAO;QACP,aAAa;QACb,oBAAM,yOAAC;;;;;QACN,GAAG,KAAK;;;;;;;MALA;AASN,MAAM,YAAY;QAAC,EAAE,QAAQ,gBAAgB,EAAE,GAAG,OAAO;yBAC9D,yOAAC;QACC,MAAK;QACL,OAAO;QACP,aAAY;QACZ,oBAAM,yOAAC;;;;;QACN,GAAG,KAAK;;;;;;;MANA;AAUN,MAAM,cAAc,CAAC,sBAC1B,yOAAC;QACC,aAAY;QACZ,oBAAM,yOAAC;;;;;QACN,GAAG,KAAK;;;;;;MAJA;AAQN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QACC,OAAM;QACN,aAAY;QACZ,QAAQ;QACP,GAAG,KAAK;;;;;;MALA;AASN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QACC,OAAM;QACN,aAAY;QACZ,OAAM;QACL,GAAG,KAAK;;;;;;OALA;AASN,MAAM,kBAAkB,CAAC,sBAC9B,yOAAC;QACC,OAAM;QACN,aAAY;QACZ,YAAW;QACX,MAAK;QACJ,GAAG,KAAK;;;;;;OANA;AAWN,MAAM,YAAY,CAAC,sBACxB,yOAAC;QAAc,OAAM;QAAO,aAAY;QAAmB,GAAG,KAAK;;;;;;OADxD;AAIN,MAAM,UAAU,CAAC,sBACtB,yOAAC;QAAc,OAAM;QAAK,aAAY;QAAmB,GAAG,KAAK;;;;;;OADtD;AAIN,MAAM,qBAAqB,CAAC,sBACjC,yOAAC;QAAU,OAAM;QAAkB,GAAG,KAAK;;;;;;OADhC;AAIN,MAAM,kBAAkB,CAAC,sBAC9B,yOAAC;QAAU,OAAM;QAAe,GAAG,KAAK;;;;;;OAD7B;uCAIE", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Button.js"], "sourcesContent": ["import React from 'react';\n\nconst Button = ({ \n  children, \n  variant = 'primary', \n  size = 'medium', \n  disabled = false, \n  loading = false, \n  fullWidth = false,\n  onClick,\n  type = 'button',\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500',\n    danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500',\n    warning: 'bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500',\n    outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500',\n    admin: 'bg-purple-600 hover:bg-purple-700 text-white focus:ring-purple-500',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-1 text-xs',\n    small: 'px-3 py-1.5 text-sm',\n    medium: 'px-4 py-2 text-sm',\n    large: 'px-6 py-3 text-base',\n    'extra-large': 'px-8 py-4 text-lg',\n  };\n\n  const widthClass = fullWidth ? 'w-full' : '';\n  \n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant] || variants.primary}\n    ${sizes[size] || sizes.medium}\n    ${widthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg \n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\" \n          fill=\"none\" \n          viewBox=\"0 0 24 24\"\n        >\n          <circle \n            className=\"opacity-25\" \n            cx=\"12\" \n            cy=\"12\" \n            r=\"10\" \n            stroke=\"currentColor\" \n            strokeWidth=\"4\"\n          />\n          <path \n            className=\"opacity-75\" \n            fill=\"currentColor\" \n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\n// Flight Booking Buttons\nexport const SearchFlightButton = (props) => (\n  <Button variant=\"primary\" {...props}>Search Flight</Button>\n);\n\nexport const BookNowButton = (props) => (\n  <Button variant=\"primary\" {...props}>Book Now</Button>\n);\n\nexport const CancelButton = (props) => (\n  <Button variant=\"secondary\" {...props}>Cancel</Button>\n);\n\nexport const ViewDetailsButton = (props) => (\n  <Button variant=\"outline\" {...props}>View Details</Button>\n);\n\nexport const SkipButton = (props) => (\n  <Button variant=\"ghost\" {...props}>Skip</Button>\n);\n\n// Admin Buttons\nexport const SaveChangesButton = (props) => (\n  <Button variant=\"admin\" {...props}>Save Changes</Button>\n);\n\nexport const EditButton = (props) => (\n  <Button variant=\"outline\" {...props}>Edit</Button>\n);\n\nexport const AddOptionsButton = (props) => (\n  <Button variant=\"admin\" {...props}>+ Add Options</Button>\n);\n\n// Status Buttons\nexport const ConfirmButton = (props) => (\n  <Button variant=\"success\" {...props}>Confirm</Button>\n);\n\nexport const DeleteButton = (props) => (\n  <Button variant=\"danger\" {...props}>Delete</Button>\n);\n\nexport const PendingButton = (props) => (\n  <Button variant=\"warning\" {...props}>Pending</Button>\n);\n\n// Loading Button\nexport const LoadingButton = (props) => (\n  <Button loading {...props}>Loading...</Button>\n);\n\n// Disabled Button\nexport const DisabledButton = (props) => (\n  <Button disabled variant=\"secondary\" {...props}>Disabled</Button>\n);\n\nexport default Button;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,SAAS;QAAC,EACd,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,OAAO,EACP,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;QACP,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,aAAa,YAAY,WAAW;IAE1C,MAAM,gBAAgB,AAAC,SAEnB,OADA,aAAY,UAEZ,OADA,QAAQ,CAAC,QAAQ,IAAI,SAAS,OAAO,EAAC,UAEtC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,MAAM,EAAC,UAE5B,OADA,YAAW,UACD,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QACC,MAAM;QACN,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,yOAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;;kCAER,yOAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,yOAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA3EM;AA8EC,MAAM,qBAAqB,CAAC,sBACjC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD5B;AAIN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,mBAAmB,CAAC,sBAC/B,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAU,GAAG,KAAK;kBAAE;;;;;;OADzB;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD1B;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADhB;AAKN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAO,QAAQ;QAAC,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OADrC;uCAIE", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/auth/LoginForm.js"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from './AuthProvider';\nimport Input from '../ui/Input';\nimport Button from '../ui/Button';\n\nconst LoginForm = () => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n  });\n  const [errors, setErrors] = useState({});\n  \n  const { login, loading, error, clearError, user } = useAuth();\n  const router = useRouter();\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    \n    // Clear field error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: '',\n      }));\n    }\n    \n    // Clear general error\n    if (error) {\n      clearError();\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    }\n    \n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    const result = await login(formData);\n\n    if (result.success) {\n      // The login function in AuthProvider should have updated the user state\n      // We can access the user from the result or wait for the context to update\n      // Let's wait a bit for the auth context to update and then redirect\n      setTimeout(() => {\n        // Check the updated user from auth context\n        if (user && user.role) {\n          switch (user.role) {\n            case 'admin':\n              router.push('/admin');\n              break;\n            case 'trainer':\n              router.push('/trainer');\n              break;\n            case 'customer':\n              router.push('/customer');\n              break;\n            default:\n              router.push('/');\n          }\n        } else {\n          // If user is still not available, redirect to customer as fallback\n          router.push('/customer');\n        }\n      }, 500); // Increased delay to ensure auth state is updated\n    }\n  };\n\n  const handleDemoLogin = async (role) => {\n    const demoCredentials = {\n      admin: { username: 'admin', password: 'admin' },\n      trainer: { username: 'sarah_johnson', password: 'trainer' },\n      customer: { username: 'customer', password: 'customer' },\n    };\n\n    setFormData(demoCredentials[role]);\n\n    const result = await login(demoCredentials[role]);\n\n    if (result.success) {\n      // Wait for auth state to update and then redirect\n      setTimeout(() => {\n        router.push(`/${role}`);\n      }, 500);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Sign in to your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Multi-User Authentication System\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <Input\n              label=\"Username\"\n              name=\"username\"\n              type=\"text\"\n              required\n              placeholder=\"Enter your username\"\n              value={formData.username}\n              onChange={handleChange}\n              error={errors.username}\n              disabled={loading}\n            />\n            \n            <Input\n              label=\"Password\"\n              name=\"password\"\n              type=\"password\"\n              required\n              placeholder=\"Enter your password\"\n              value={formData.password}\n              onChange={handleChange}\n              error={errors.password}\n              disabled={loading}\n            />\n          </div>\n\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          <div>\n            <Button\n              type=\"submit\"\n              fullWidth\n              loading={loading}\n              disabled={loading}\n            >\n              {loading ? 'Signing in...' : 'Sign in'}\n            </Button>\n          </div>\n        </form>\n\n        {/* Demo Login Buttons */}\n        <div className=\"mt-8\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-gray-50 text-gray-500\">Demo Accounts</span>\n            </div>\n          </div>\n\n          <div className=\"mt-6 grid grid-cols-1 gap-3\">\n            <Button\n              variant=\"outline\"\n              fullWidth\n              onClick={() => handleDemoLogin('admin')}\n              disabled={loading}\n            >\n              Login as Admin\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              fullWidth\n              onClick={() => handleDemoLogin('trainer')}\n              disabled={loading}\n            >\n              Login as Trainer\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              fullWidth\n              onClick={() => handleDemoLogin('customer')}\n              disabled={loading}\n            >\n              Login as Customer\n            </Button>\n          </div>\n        </div>\n\n        {/* Demo Credentials Info */}\n        <div className=\"mt-6 bg-blue-50 border border-blue-200 rounded-md p-4\">\n          <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials:</h3>\n          <div className=\"text-xs text-blue-700 space-y-1\">\n            <div><strong>Admin:</strong> username: admin, password: admin</div>\n            <div><strong>Trainer:</strong> username: sarah_johnson, password: trainer</div>\n            <div><strong>Customer:</strong> username: customer, password: customer</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginForm;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,YAAY;;IAChB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,qNAAQ,EAAC;QACvC,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,qNAAQ,EAAC,CAAC;IAEtC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,IAAA,mMAAO;IAC3D,MAAM,SAAS,IAAA,8LAAS;IAExB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;QAEA,sBAAsB;QACtB,IAAI,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,CAAC;QAEnB,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,OAAO,OAAO,EAAE;YAClB,wEAAwE;YACxE,2EAA2E;YAC3E,oEAAoE;YACpE,WAAW;gBACT,2CAA2C;gBAC3C,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACrB,OAAQ,KAAK,IAAI;wBACf,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF;4BACE,OAAO,IAAI,CAAC;oBAChB;gBACF,OAAO;oBACL,mEAAmE;oBACnE,OAAO,IAAI,CAAC;gBACd;YACF,GAAG,MAAM,kDAAkD;QAC7D;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,MAAM,kBAAkB;YACtB,OAAO;gBAAE,UAAU;gBAAS,UAAU;YAAQ;YAC9C,SAAS;gBAAE,UAAU;gBAAiB,UAAU;YAAU;YAC1D,UAAU;gBAAE,UAAU;gBAAY,UAAU;YAAW;QACzD;QAEA,YAAY,eAAe,CAAC,KAAK;QAEjC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC,KAAK;QAEhD,IAAI,OAAO,OAAO,EAAE;YAClB,kDAAkD;YAClD,WAAW;gBACT,OAAO,IAAI,CAAC,AAAC,IAAQ,OAAL;YAClB,GAAG;QACL;IACF;IAEA,qBACE,yOAAC;QAAI,WAAU;kBACb,cAAA,yOAAC;YAAI,WAAU;;8BACb,yOAAC;;sCACC,yOAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,yOAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;8BAKxD,yOAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,yOAAC;4BAAI,WAAU;;8CACb,yOAAC,0LAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,aAAY;oCACZ,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,OAAO,OAAO,QAAQ;oCACtB,UAAU;;;;;;8CAGZ,yOAAC,0LAAK;oCACJ,OAAM;oCACN,MAAK;oCACL,MAAK;oCACL,QAAQ;oCACR,aAAY;oCACZ,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,OAAO,OAAO,QAAQ;oCACtB,UAAU;;;;;;;;;;;;wBAIb,uBACC,yOAAC;4BAAI,WAAU;sCACb,cAAA,yOAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;sCAI3C,yOAAC;sCACC,cAAA,yOAAC,2LAAM;gCACL,MAAK;gCACL,SAAS;gCACT,SAAS;gCACT,UAAU;0CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;8BAMnC,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;sCAIpD,yOAAC;4BAAI,WAAU;;8CACb,yOAAC,2LAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,SAAS,IAAM,gBAAgB;oCAC/B,UAAU;8CACX;;;;;;8CAID,yOAAC,2LAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,SAAS,IAAM,gBAAgB;oCAC/B,UAAU;8CACX;;;;;;8CAID,yOAAC,2LAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,SAAS,IAAM,gBAAgB;oCAC/B,UAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;;sDAAI,yOAAC;sDAAO;;;;;;wCAAe;;;;;;;8CAC5B,yOAAC;;sDAAI,yOAAC;sDAAO;;;;;;wCAAiB;;;;;;;8CAC9B,yOAAC;;sDAAI,yOAAC;sDAAO;;;;;;wCAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GApNM;;QAOgD,mMAAO;QAC5C,8LAAS;;;KARpB;uCAsNS", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Navbar.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Button from '../ui/Button';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [user, setUser] = useState(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is logged in\n    const token = localStorage.getItem('accessToken');\n    const userData = localStorage.getItem('user');\n\n    if (token && userData) {\n      setIsLoggedIn(true);\n      setUser(JSON.parse(userData));\n    }\n  }, []);\n\n  const handleLogout = () => {\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('user');\n    setIsLoggedIn(false);\n    setUser(null);\n    router.push('/');\n  };\n\n  const getDashboardLink = () => {\n    if (!user) return '/';\n\n    switch (user.role) {\n      case 'admin':\n        return '/admin';\n      case 'trainer':\n        return '/trainer';\n      case 'customer':\n        return '/customer';\n      default:\n        return '/';\n    }\n  };\n\n  const navLinks = [\n    { href: '#plans', label: 'Plans' },\n    { href: '#trainers', label: 'Trainers' },\n    { href: '#testimonials', label: 'Success Stories' },\n    { href: '#contact', label: 'Contact' },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center cursor-pointer\" onClick={() => router.push('/')}>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">F</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  FitLife Gym\n                </h1>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:ml-8 md:flex md:space-x-8\">\n              {navLinks.map((link) => (\n                <a\n                  key={link.href}\n                  href={link.href}\n                  className=\"text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n                >\n                  {link.label}\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {isLoggedIn && user ? (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"hidden md:flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome,</span>\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {user.username}\n                  </span>\n                </div>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push(getDashboardLink())}\n                >\n                  Dashboard\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={handleLogout}\n                >\n                  Logout\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push('/login')}\n                >\n                  Login\n                </Button>\n\n                <Button\n                  size=\"small\"\n                  className=\"bg-yellow-400 hover:bg-yellow-500 text-black\"\n                  onClick={() => router.push('/register')}\n                >\n                  Join Now\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 p-2 rounded-md\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {!isMenuOpen ? (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  </svg>\n                ) : (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t border-gray-200\">\n            {/* Mobile Navigation Links */}\n            {navLinks.map((link) => (\n              <a\n                key={link.href}\n                href={link.href}\n                className=\"text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {link.label}\n              </a>\n            ))}\n\n            {/* Mobile User Actions */}\n            <div className=\"border-t border-gray-200 pt-4 mt-4\">\n              {isLoggedIn && user ? (\n                <div className=\"space-y-2\">\n                  <div className=\"px-3 py-2\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      Welcome, {user.username}\n                    </div>\n                    <div className=\"text-xs text-gray-600 capitalize\">\n                      {user.role}\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      router.push(getDashboardLink());\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Dashboard\n                  </button>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  <button\n                    onClick={() => {\n                      router.push('/login');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Login\n                  </button>\n                  <button\n                    onClick={() => {\n                      router.push('/register');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left bg-yellow-400 hover:bg-yellow-500 text-black block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Join Now\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,qNAAQ,EAAC;IACjC,MAAM,SAAS,IAAA,8LAAS;IAExB,IAAA,sNAAS;4BAAC;YACR,6BAA6B;YAC7B,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,SAAS,UAAU;gBACrB,cAAc;gBACd,QAAQ,KAAK,KAAK,CAAC;YACrB;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAkB;QAClD;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,yOAAC;QAAI,WAAU;;0BACb,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;oCAAiD,SAAS,IAAM,OAAO,IAAI,CAAC;8CACzF,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,yOAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAOpD,yOAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,KAAK;2CAJN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAWtB,yOAAC;4BAAI,WAAU;;gCACZ,cAAc,qBACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,yOAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;;;;;;;sDAIlB,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDACV;;;;;;;;;;;6FAKH,yOAAC;oCAAI,WAAU;;sDACb,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;8CAOL,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,yOAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,CAAC,2BACA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;yGAGvE,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,4BACC,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;wBAEZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;sCAUlB,yOAAC;4BAAI,WAAU;sCACZ,cAAc,qBACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;;oDAAoC;oDACvC,KAAK,QAAQ;;;;;;;0DAEzB,yOAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;;;;;;;kDAGd,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP;4CACA,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;yFAKH,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA/NM;;QAIW,8LAAS;;;KAJpB;uCAiOS", "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Layout.js"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Navbar from './Navbar';\nimport { useAuth } from '../auth/AuthProvider';\n\nconst Layout = ({ children, showNavbar = true, className = '' }) => {\n  const { isAuthenticated } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {showNavbar && isAuthenticated && <Navbar />}\n      \n      <main className={`${className}`}>\n        {children}\n      </main>\n    </div>\n  );\n};\n\n// Dashboard Layout with sidebar\nexport const DashboardLayout = ({ \n  children, \n  title, \n  subtitle,\n  actions,\n  className = '' \n}) => {\n  return (\n    <Layout className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <div className=\"md:flex md:items-center md:justify-between\">\n            <div className=\"flex-1 min-w-0\">\n              <h1 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n                {title}\n              </h1>\n              {subtitle && (\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {subtitle}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Page Content */}\n        <div className={className}>\n          {children}\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\n// Card Layout for content sections\nexport const CardLayout = ({ \n  children, \n  title, \n  subtitle,\n  actions,\n  className = '',\n  padding = true \n}) => {\n  return (\n    <div className={`bg-white shadow rounded-lg ${className}`}>\n      {(title || subtitle || actions) && (\n        <div className={`${padding ? 'px-4 py-5 sm:px-6' : 'px-6 py-4'} border-b border-gray-200`}>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              {title && (\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                  {title}\n                </h3>\n              )}\n              {subtitle && (\n                <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                  {subtitle}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex space-x-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n      \n      <div className={padding ? 'px-4 py-5 sm:p-6' : ''}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Stats Layout for dashboard metrics\nexport const StatsLayout = ({ stats = [] }) => {\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n      {stats.map((stat, index) => (\n        <div key={index} className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                {stat.icon && (\n                  <div className={`w-8 h-8 ${stat.iconColor || 'text-gray-400'}`}>\n                    {stat.icon}\n                  </div>\n                )}\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    {stat.label}\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {stat.value}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          {stat.change && (\n            <div className=\"bg-gray-50 px-5 py-3\">\n              <div className=\"text-sm\">\n                <span className={`font-medium ${\n                  stat.changeType === 'increase' ? 'text-green-600' : \n                  stat.changeType === 'decrease' ? 'text-red-600' : \n                  'text-gray-600'\n                }`}>\n                  {stat.change}\n                </span>\n                <span className=\"text-gray-500\"> from last month</span>\n              </div>\n            </div>\n          )}\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// Grid Layout for responsive content\nexport const GridLayout = ({ \n  children, \n  cols = 1, \n  gap = 6, \n  className = '' \n}) => {\n  const gridClasses = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\n  };\n\n  const gapClasses = {\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  return (\n    <div className={`grid ${gridClasses[cols]} ${gapClasses[gap]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;QAAC,EAAE,QAAQ,EAAE,aAAa,IAAI,EAAE,YAAY,EAAE,EAAE;;IAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,mMAAO;IAEnC,qBACE,yOAAC;QAAI,WAAU;;YACZ,cAAc,iCAAmB,yOAAC,+LAAM;;;;;0BAEzC,yOAAC;gBAAK,WAAW,AAAC,GAAY,OAAV;0BACjB;;;;;;;;;;;;AAIT;GAZM;;QACwB,mMAAO;;;KAD/B;AAeC,MAAM,kBAAkB;QAAC,EAC9B,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACf;IACC,qBACE,yOAAC;QAAO,WAAU;kBAChB,cAAA,yOAAC;YAAI,WAAU;;8BAEb,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,0BACC,yOAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;4BAIN,yBACC,yOAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;8BAOT,yOAAC;oBAAI,WAAW;8BACb;;;;;;;;;;;;;;;;;AAKX;MAtCa;AAyCN,MAAM,aAAa;QAAC,EACzB,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACd,UAAU,IAAI,EACf;IACC,qBACE,yOAAC;QAAI,WAAW,AAAC,8BAAuC,OAAV;;YAC3C,CAAC,SAAS,YAAY,OAAO,mBAC5B,yOAAC;gBAAI,WAAW,AAAC,GAA8C,OAA5C,UAAU,sBAAsB,aAAY;0BAC7D,cAAA,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;;gCACE,uBACC,yOAAC;oCAAG,WAAU;8CACX;;;;;;gCAGJ,0BACC,yOAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;wBAIN,yBACC,yOAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAOX,yOAAC;gBAAI,WAAW,UAAU,qBAAqB;0BAC5C;;;;;;;;;;;;AAIT;MAvCa;AA0CN,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAE,EAAE;IACxC,qBACE,yOAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,yOAAC;gBAAgB,WAAU;;kCACzB,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,kBACR,yOAAC;wCAAI,WAAW,AAAC,WAA4C,OAAlC,KAAK,SAAS,IAAI;kDAC1C,KAAK,IAAI;;;;;;;;;;;8CAIhB,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;;0DACC,yOAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,yOAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAMpB,KAAK,MAAM,kBACV,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAK,WAAW,AAAC,eAIjB,OAHC,KAAK,UAAU,KAAK,aAAa,mBACjC,KAAK,UAAU,KAAK,aAAa,iBACjC;8CAEC,KAAK,MAAM;;;;;;8CAEd,yOAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;eAhC9B;;;;;;;;;;AAwClB;MA5Ca;AA+CN,MAAM,aAAa;QAAC,EACzB,QAAQ,EACR,OAAO,CAAC,EACR,MAAM,CAAC,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,aAAa;QACjB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,yOAAC;QAAI,WAAW,AAAC,QAA4B,OAArB,WAAW,CAAC,KAAK,EAAC,KAAsB,OAAnB,UAAU,CAAC,IAAI,EAAC,KAAa,OAAV;kBAC7D;;;;;;AAGP;MAxBa;uCA0BE", "debugId": null}}, {"offset": {"line": 1861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/login/page.js"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport LoginForm from '@/components/auth/LoginForm';\nimport Layout from '@/components/common/Layout';\n\nexport default function LoginPage() {\n  return (\n    <Layout showNavbar={false}>\n      <LoginForm />\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,yOAAC,+LAAM;QAAC,YAAY;kBAClB,cAAA,yOAAC,gMAAS;;;;;;;;;;AAGhB;KANwB", "debugId": null}}]}