module.exports = [
"[project]/Documents/VS CODE/gym/frontend/.next-internal/server/app/api/subscriptions/trainers/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/Documents/VS CODE/gym/frontend/src/app/api/subscriptions/trainers/route.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/VS CODE/gym/frontend/node_modules/next/server.js [app-route] (ecmascript)");
;
const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL || 'http://localhost:8000';
async function GET(request) {
    try {
        // Forward request to Auth Service to get trainers
        const response = await fetch(`${AUTH_SERVICE_URL}/users?role=trainer`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            // If the auth service is not available, return mock data
            const mockTrainers = [
                {
                    id: 1,
                    specialization: "Weight Loss & Cardio",
                    current_clients: 8,
                    max_clients: 15,
                    rating: 4.9
                },
                {
                    id: 2,
                    specialization: "Strength Training",
                    current_clients: 12,
                    max_clients: 15,
                    rating: 4.8
                },
                {
                    id: 3,
                    specialization: "Yoga & Flexibility",
                    current_clients: 6,
                    max_clients: 12,
                    rating: 4.7
                },
                {
                    id: 4,
                    specialization: "CrossFit & HIIT",
                    current_clients: 15,
                    max_clients: 15,
                    rating: 4.9
                }
            ];
            return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockTrainers);
        }
        const data = await response.json();
        // Transform user data to trainer format
        const trainers = data.map((user)=>({
                id: user.id,
                specialization: user.full_name || user.username,
                current_clients: Math.floor(Math.random() * 15),
                max_clients: 15,
                rating: (4.5 + Math.random() * 0.5).toFixed(1)
            }));
        return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(trainers);
    } catch (error) {
        console.error('Trainers fetch error:', error);
        // Return mock data as fallback
        const mockTrainers = [
            {
                id: 1,
                specialization: "Weight Loss & Cardio",
                current_clients: 8,
                max_clients: 15,
                rating: 4.9
            },
            {
                id: 2,
                specialization: "Strength Training",
                current_clients: 12,
                max_clients: 15,
                rating: 4.8
            },
            {
                id: 3,
                specialization: "Yoga & Flexibility",
                current_clients: 6,
                max_clients: 12,
                rating: 4.7
            },
            {
                id: 4,
                specialization: "CrossFit & HIIT",
                current_clients: 15,
                max_clients: 15,
                rating: 4.9
            }
        ];
        return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockTrainers);
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__124c16c6._.js.map