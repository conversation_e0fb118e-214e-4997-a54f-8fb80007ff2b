'use client';

import React from 'react';
import Badge from '@/components/ui/Badge';

const TrainerSection = () => {
  const trainers = [
    {
      id: 1,
      name: "<PERSON>",
      specialization: "Weight Loss & Cardio",
      experience: "8 years",
      certifications: ["NASM-CPT", "Nutrition Specialist"],
      bio: "Specializes in helping clients achieve sustainable weight loss through personalized cardio and strength training programs.",
      image: "/api/placeholder/300/300",
      rating: 4.9,
      clients: 150
    },
    {
      id: 2,
      name: "<PERSON>",
      specialization: "Strength Training & Bodybuilding",
      experience: "12 years",
      certifications: ["ACSM-CPT", "Powerlifting Coach"],
      bio: "Expert in muscle building and strength development with a focus on proper form and progressive overload.",
      image: "/api/placeholder/300/300",
      rating: 4.8,
      clients: 200
    },
    {
      id: 3,
      name: "<PERSON>",
      specialization: "Yoga & Flexibility",
      experience: "6 years",
      certifications: ["RYT-500", "Pilates Instructor"],
      bio: "Combines traditional yoga practices with modern fitness techniques for improved flexibility and mindfulness.",
      image: "/api/placeholder/300/300",
      rating: 4.9,
      clients: 120
    },
    {
      id: 4,
      name: "<PERSON>",
      specialization: "HIIT & Functional Training",
      experience: "10 years",
      certifications: ["CrossFit Level 2", "TRX Certified"],
      bio: "High-intensity interval training expert focused on functional movements and athletic performance.",
      image: "/api/placeholder/300/300",
      rating: 4.7,
      clients: 180
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Meet Our Expert Trainers
          </h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Our certified personal trainers are here to guide you every step of the way. 
            Each trainer brings unique expertise to help you achieve your specific goals.
          </p>
        </div>

        {/* Trainers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {trainers.map((trainer) => (
            <div
              key={trainer.id}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
            >
              {/* Trainer Image */}
              <div className="relative h-64 bg-gray-300">
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <div className="flex items-center space-x-1 mb-1">
                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="text-sm font-medium">{trainer.rating}</span>
                  </div>
                  <div className="text-xs opacity-90">{trainer.clients} clients</div>
                </div>
              </div>

              {/* Trainer Info */}
              <div className="p-6">
                {/* Name and Specialization */}
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {trainer.name}
                </h3>
                <p className="text-blue-600 font-medium mb-3">
                  {trainer.specialization}
                </p>

                {/* Experience */}
                <div className="flex items-center mb-3">
                  <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm text-gray-600">{trainer.experience} experience</span>
                </div>

                {/* Certifications */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {trainer.certifications.map((cert, index) => (
                    <Badge key={index} variant="secondary" size="small">
                      {cert}
                    </Badge>
                  ))}
                </div>

                {/* Bio */}
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {trainer.bio}
                </p>

                {/* Action Button */}
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                  View Profile
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Trainer Stats */}
        <div className="mt-16 bg-white rounded-lg shadow-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-gray-600">Certified Trainers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">10,000+</div>
              <div className="text-gray-600">Training Sessions</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">95%</div>
              <div className="text-gray-600">Client Satisfaction</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
              <div className="text-gray-600">Support Available</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Meet Your Perfect Trainer?
          </h3>
          <p className="text-gray-600 mb-6">
            Subscribe to any plan and get matched with a trainer who fits your goals and schedule.
          </p>
          <button className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-8 py-3 rounded-lg transition-colors">
            Find My Trainer
          </button>
        </div>
      </div>
    </section>
  );
};

export default TrainerSection;
