import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';

export async function POST(request) {
  try {
    const cookieStore = await cookies();
    const refreshToken = cookieStore.get('refreshToken')?.value;

    // Always clear the refresh token cookie
    cookieStore.delete('refreshToken');

    if (refreshToken) {
      try {
        // Notify backend to invalidate the token
        await fetch(`${BACKEND_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${refreshToken}`,
          },
        });
      } catch (error) {
        // Log error but don't fail the logout process
        console.error('Backend logout error:', error);
      }
    }

    return NextResponse.json({ message: 'Logged out successfully' });

  } catch (error) {
    console.error('Logout error:', error);
    // Even if there's an error, clear the cookie and return success
    const cookieStore = await cookies();
    cookieStore.delete('refreshToken');

    return NextResponse.json({ message: 'Logged out successfully' });
  }
}
