from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import json

from ..core.database import get_db
from ..schemas.plan import PlanResponse, PlanCreate, PlanUpdate, PlanPublic
from ..services.plan_service import PlanService

router = APIRouter()

@router.get("/public", response_model=List[PlanPublic])
async def get_public_plans(db: Session = Depends(get_db)):
    """Get active plans for public display on the main website."""
    plans = PlanService.get_public_plans(db)
    
    # Convert plans to response format with parsed features
    public_plans = []
    for plan in plans:
        features = PlanService.parse_features(plan)
        public_plans.append(PlanPublic(
            id=plan.id,
            name=plan.name,
            description=plan.description,
            price=plan.price,
            duration_months=plan.duration_months,
            features=features
        ))
    
    return public_plans

@router.get("/", response_model=List[PlanResponse])
async def get_plans(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """Get list of plans (admin access)."""
    plans = PlanService.get_plans(db, skip=skip, limit=limit, active_only=active_only)
    
    # Convert plans to response format with parsed features
    plan_responses = []
    for plan in plans:
        features = PlanService.parse_features(plan)
        plan_responses.append(PlanResponse(
            id=plan.id,
            name=plan.name,
            description=plan.description,
            price=plan.price,
            duration_months=plan.duration_months,
            features=features,
            is_active=plan.is_active,
            created_at=plan.created_at,
            updated_at=plan.updated_at
        ))
    
    return plan_responses

@router.post("/", response_model=PlanResponse)
async def create_plan(
    plan: PlanCreate,
    db: Session = Depends(get_db)
):
    """Create a new gym plan (admin only)."""
    # For now, we'll use a default created_by value
    # In a real implementation, this would come from the authenticated user
    created_by = 1  # Admin user ID
    
    db_plan = PlanService.create_plan(db, plan, created_by)
    features = PlanService.parse_features(db_plan)
    
    return PlanResponse(
        id=db_plan.id,
        name=db_plan.name,
        description=db_plan.description,
        price=db_plan.price,
        duration_months=db_plan.duration_months,
        features=features,
        is_active=db_plan.is_active,
        created_at=db_plan.created_at,
        updated_at=db_plan.updated_at
    )

@router.get("/{plan_id}", response_model=PlanResponse)
async def get_plan(plan_id: int, db: Session = Depends(get_db)):
    """Get plan by ID."""
    plan = PlanService.get_plan(db, plan_id)
    if plan is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    features = PlanService.parse_features(plan)
    
    return PlanResponse(
        id=plan.id,
        name=plan.name,
        description=plan.description,
        price=plan.price,
        duration_months=plan.duration_months,
        features=features,
        is_active=plan.is_active,
        created_at=plan.created_at,
        updated_at=plan.updated_at
    )

@router.put("/{plan_id}", response_model=PlanResponse)
async def update_plan(
    plan_id: int,
    plan_update: PlanUpdate,
    db: Session = Depends(get_db)
):
    """Update plan information (admin only)."""
    updated_plan = PlanService.update_plan(db, plan_id, plan_update)
    if updated_plan is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    features = PlanService.parse_features(updated_plan)
    
    return PlanResponse(
        id=updated_plan.id,
        name=updated_plan.name,
        description=updated_plan.description,
        price=updated_plan.price,
        duration_months=updated_plan.duration_months,
        features=features,
        is_active=updated_plan.is_active,
        created_at=updated_plan.created_at,
        updated_at=updated_plan.updated_at
    )

@router.delete("/{plan_id}")
async def delete_plan(plan_id: int, db: Session = Depends(get_db)):
    """Delete plan (admin only) - soft delete."""
    success = PlanService.delete_plan(db, plan_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    return {"message": "Plan deleted successfully"}

@router.delete("/{plan_id}/hard")
async def hard_delete_plan(plan_id: int, db: Session = Depends(get_db)):
    """Permanently delete plan (admin only)."""
    success = PlanService.hard_delete_plan(db, plan_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    return {"message": "Plan permanently deleted"}
