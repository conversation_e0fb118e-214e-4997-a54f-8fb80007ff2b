(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/Documents/VS CODE/gym/frontend/node_modules/next/error.js [client] (ecmascript)", ((__turbopack_context__, module, exports) => {

module.exports = __turbopack_context__.r("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/pages/_error.js [client] (ecmascript)");
}),
]);

//# sourceMappingURL=5991a_next_error_3c5471d7.js.map