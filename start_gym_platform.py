#!/usr/bin/env python3
"""
Comprehensive startup script for FitLife Gym Platform.
Starts all microservices and the frontend application.
"""

import os
import sys
import subprocess
import time
import threading
from pathlib import Path

def run_service(service_name, port, service_path):
    """Start a microservice."""
    print(f"🚀 Starting {service_name} on port {port}...")
    
    try:
        os.chdir(service_path)
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print(f"\n🛑 {service_name} stopped")
    except Exception as e:
        print(f"❌ Error starting {service_name}: {e}")
    finally:
        # Return to project root
        os.chdir("../..")

def run_frontend():
    """Start the Next.js frontend."""
    print("🚀 Starting Next.js frontend...")
    frontend_dir = Path("frontend")
    
    try:
        os.chdir(frontend_dir)
        subprocess.run(["npm", "run", "dev"])
    except KeyboardInterrupt:
        print("\n🛑 Frontend stopped")
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")
    finally:
        os.chdir("..")

def initialize_database():
    """Initialize the database with seed data."""
    print("🗄️ Initializing database...")
    try:
        os.chdir("database")
        result = subprocess.run([sys.executable, "init_db.py"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Database initialized successfully")
            print(result.stdout)
        else:
            print("❌ Database initialization failed")
            print(result.stderr)
        
        os.chdir("..")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        os.chdir("..")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    print("🔍 Checking dependencies...")
    
    # Check Node.js and npm
    try:
        subprocess.run(["node", "--version"], check=True, capture_output=True)
        subprocess.run(["npm", "--version"], check=True, capture_output=True)
        print("✅ Node.js and npm are installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Node.js and npm are required but not found")
        return False
    
    # Check Python
    try:
        subprocess.run([sys.executable, "--version"], check=True, capture_output=True)
        print("✅ Python is installed")
    except subprocess.CalledProcessError:
        print("❌ Python is required but not found")
        return False
    
    return True

def install_dependencies():
    """Install project dependencies."""
    print("📦 Installing dependencies...")
    
    # Install frontend dependencies
    frontend_dir = Path("frontend")
    if frontend_dir.exists():
        print("Installing frontend dependencies...")
        try:
            os.chdir(frontend_dir)
            subprocess.run(["npm", "install"], check=True)
            print("✅ Frontend dependencies installed")
            os.chdir("..")
        except subprocess.CalledProcessError:
            print("❌ Failed to install frontend dependencies")
            return False
    
    # Install backend dependencies for each microservice
    services = [
        "auth_service",
        "plan_service", 
        "subscription_service"
    ]
    
    for service in services:
        service_dir = Path(f"backend/microservices/{service}")
        if service_dir.exists():
            print(f"Installing {service} dependencies...")
            try:
                os.chdir(service_dir)
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
                print(f"✅ {service} dependencies installed")
                os.chdir("../../..")
            except subprocess.CalledProcessError:
                print(f"❌ Failed to install {service} dependencies")
                return False
    
    return True

def main():
    """Main function to start the gym platform."""
    print("=" * 60)
    print("🏋️ FitLife Gym - Online Consultation Platform")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("frontend").exists() or not Path("backend").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install required software.")
        sys.exit(1)
    
    # Ask user if they want to install dependencies
    install_deps = input("\n📦 Install/update dependencies? (y/N): ").lower().strip()
    if install_deps in ['y', 'yes']:
        if not install_dependencies():
            print("❌ Dependency installation failed")
            sys.exit(1)
    
    # Initialize database
    init_db = input("\n🗄️ Initialize database with seed data? (y/N): ").lower().strip()
    if init_db in ['y', 'yes']:
        if not initialize_database():
            print("⚠️ Database initialization failed, but continuing...")
    
    print("\n🚀 Starting FitLife Gym Platform...")
    print("\n📍 Service URLs:")
    print("Frontend:           http://localhost:3000")
    print("Auth Service:       http://localhost:8000")
    print("Plan Service:       http://localhost:8002")
    print("Subscription Service: http://localhost:8003")
    print("\n📚 API Documentation:")
    print("Auth Service:       http://localhost:8000/docs")
    print("Plan Service:       http://localhost:8002/docs")
    print("Subscription Service: http://localhost:8003/docs")
    print("\n🔑 Demo Credentials:")
    print("Admin:    username=admin,    password=admin")
    print("Trainer:  username=sarah_johnson, password=trainer")
    print("Customer: username=customer, password=customer")
    print("\nPress Ctrl+C to stop all services")
    print("-" * 60)
    
    # Start microservices in separate threads
    services = [
        ("Auth Service", 8000, "backend/microservices/auth_service"),
        ("Plan Service", 8002, "backend/microservices/plan_service"),
        ("Subscription Service", 8003, "backend/microservices/subscription_service"),
    ]
    
    threads = []
    for service_name, port, path in services:
        if Path(path).exists():
            thread = threading.Thread(target=run_service, args=(service_name, port, path), daemon=True)
            thread.start()
            threads.append(thread)
            time.sleep(2)  # Stagger service startup
    
    # Wait a moment for services to start
    time.sleep(5)
    
    try:
        # Start frontend (this will block)
        run_frontend()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down FitLife Gym Platform...")
        print("✅ All services stopped successfully")

if __name__ == "__main__":
    main()
