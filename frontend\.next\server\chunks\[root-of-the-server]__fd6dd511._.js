module.exports = [
"[project]/Documents/VS CODE/gym/frontend/.next-internal/server/app/api/subscriptions/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/Documents/VS CODE/gym/frontend/src/app/api/subscriptions/route.js [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/VS CODE/gym/frontend/node_modules/next/server.js [app-route] (ecmascript)");
;
const SUBSCRIPTION_SERVICE_URL = process.env.SUBSCRIPTION_SERVICE_URL || 'http://localhost:8003';
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const userId = searchParams.get('user_id');
        const trainerId = searchParams.get('trainer_id');
        const pending = searchParams.get('pending') === 'true';
        let endpoint = '/subscriptions/';
        if (pending) {
            endpoint = '/subscriptions/pending';
        } else if (userId) {
            endpoint = `/subscriptions/user/${userId}`;
        } else if (trainerId) {
            endpoint = `/subscriptions/trainer/${trainerId}`;
        }
        try {
            // Forward request to Subscription Service
            const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.detail || 'Failed to fetch subscriptions');
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(data);
        } catch (fetchError) {
            console.log('Subscription service not available, returning mock data');
            // Return mock data when service is not available
            if (pending) {
                const mockPendingSubscriptions = [
                    {
                        id: 1,
                        user_id: 101,
                        user_name: "John Smith",
                        plan_id: 'premium',
                        plan_name: 'Premium Training Plan',
                        payment_amount: 59.99,
                        status: 'pending',
                        subscribed_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                        trainer_id: null
                    },
                    {
                        id: 2,
                        user_id: 102,
                        user_name: "Sarah Wilson",
                        plan_id: 'basic',
                        plan_name: 'Basic Fitness Plan',
                        payment_amount: 29.99,
                        status: 'pending',
                        subscribed_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
                        trainer_id: null
                    },
                    {
                        id: 3,
                        user_id: 103,
                        user_name: "Mike Johnson",
                        plan_id: 'premium',
                        plan_name: 'Premium Training Plan',
                        payment_amount: 59.99,
                        status: 'pending',
                        subscribed_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                        trainer_id: null
                    }
                ];
                return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockPendingSubscriptions);
            } else if (userId) {
                const mockUserSubscription = [
                    {
                        id: 1,
                        user_id: parseInt(userId),
                        plan_id: 'premium',
                        plan_name: 'Premium Training Plan',
                        payment_amount: 59.99,
                        status: 'active',
                        trainer_id: 1,
                        trainer_name: 'Sarah Johnson',
                        subscribed_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                        next_session: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2 hours from now
                    }
                ];
                return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockUserSubscription);
            } else if (trainerId) {
                const mockTrainerClients = [
                    {
                        id: 1,
                        user_id: 101,
                        user_name: "John Smith",
                        plan_id: 'premium',
                        plan_name: 'Premium Training Plan',
                        payment_amount: 59.99,
                        status: 'active',
                        trainer_id: parseInt(trainerId),
                        subscribed_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
                        last_session: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                        next_session: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString() // tomorrow
                    },
                    {
                        id: 2,
                        user_id: 104,
                        user_name: "Emma Davis",
                        plan_id: 'basic',
                        plan_name: 'Basic Fitness Plan',
                        payment_amount: 29.99,
                        status: 'active',
                        trainer_id: parseInt(trainerId),
                        subscribed_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
                        last_session: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                        next_session: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString() // 3 hours from now
                    }
                ];
                return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockTrainerClients);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json([]);
        }
    } catch (error) {
        console.error('Subscriptions fetch error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { action, ...data } = body;
        let endpoint = '/subscriptions/subscribe';
        let method = 'POST';
        // Handle different subscription actions
        if (action === 'assign_trainer') {
            endpoint = '/subscriptions/assign-trainer';
            method = 'POST';
        }
        try {
            // Forward request to Subscription Service
            const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            const responseData = await response.json();
            if (!response.ok) {
                throw new Error(responseData.detail || 'Subscription operation failed');
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(responseData);
        } catch (fetchError) {
            console.log('Subscription service not available, returning mock response');
            // Return mock success response when service is not available
            if (action === 'assign_trainer') {
                return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    message: 'Trainer assigned successfully (mock)',
                    subscription_id: data.subscription_id,
                    trainer_id: data.trainer_id
                });
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Subscription operation completed (mock)',
                ...data
            });
        }
    } catch (error) {
        console.error('Subscription operation error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$VS__CODE$2f$gym$2f$frontend$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__fd6dd511._.js.map