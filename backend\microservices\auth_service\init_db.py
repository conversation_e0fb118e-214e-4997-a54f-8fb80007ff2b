#!/usr/bin/env python3
"""
Database initialization script with seed data.
Creates the database tables and populates them with initial users.
"""

import sys
import os
from sqlalchemy.orm import Session

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database import engine, SessionLocal, create_tables
from app.models.user import User
from app.schemas.user import UserCreate
from app.services.user_service import UserService

def create_seed_users(db: Session):
    """Create initial users for testing."""

    # Check if users already exist and delete them to recreate with correct passwords
    existing_admin = UserService.get_user_by_username(db, "admin")
    if existing_admin:
        print("Seed users already exist. Deleting and recreating with correct passwords...")
        # Delete all existing users
        db.query(User).delete()
        db.commit()
        print("Existing users deleted.")
    
    # Create admin user
    admin_user = UserCreate(
        username="admin",
        email="<EMAIL>",
        password="admin",
        role="admin",
        full_name="Admin User",
        is_active=True
    )

    # Create trainer users
    sarah_trainer = UserCreate(
        username="sarah_johnson",
        email="<EMAIL>",
        password="trainer",
        role="trainer",
        full_name="Sarah Johnson",
        is_active=True
    )

    # Create customer user
    customer_user = UserCreate(
        username="customer",
        email="<EMAIL>",
        password="customer",
        role="customer",
        full_name="John Customer",
        is_active=True
    )
    
    # Additional test users
    test_users = [
        UserCreate(
            username="mike_rodriguez",
            email="<EMAIL>",
            password="trainer",
            role="trainer",
            full_name="Mike Rodriguez",
            is_active=True
        ),
        UserCreate(
            username="emily_chen",
            email="<EMAIL>",
            password="trainer",
            role="trainer",
            full_name="Emily Chen",
            is_active=True
        ),
        UserCreate(
            username="david_thompson",
            email="<EMAIL>",
            password="trainer",
            role="trainer",
            full_name="David Thompson",
            is_active=True
        ),
        UserCreate(
            username="jessica_martinez",
            email="<EMAIL>",
            password="password",
            role="customer",
            full_name="Jessica Martinez",
            is_active=True
        ),
        UserCreate(
            username="robert_chen",
            email="<EMAIL>",
            password="password",
            role="customer",
            full_name="Robert Chen",
            is_active=True
        )
    ]

    # Create all users
    users_to_create = [admin_user, sarah_trainer, customer_user] + test_users
    
    for user_data in users_to_create:
        try:
            created_user = UserService.create_user(db, user_data)
            print(f"Created user: {created_user.username} ({created_user.role})")
        except Exception as e:
            print(f"Error creating user {user_data.username}: {e}")

def init_database():
    """Initialize the database with tables and seed data."""
    
    print("Initializing database...")
    
    # Create tables
    print("Creating database tables...")
    create_tables()
    print("Database tables created successfully.")
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Create seed users
        print("Creating seed users...")
        create_seed_users(db)
        print("Seed users created successfully.")
        
        # Display created users
        print("\nCreated users:")
        users = UserService.get_users(db)
        for user in users:
            status = "Active" if user.is_active else "Inactive"
            print(f"  - {user.username} ({user.role}) - {status}")
        
        print(f"\nTotal users created: {len(users)}")
        
    except Exception as e:
        print(f"Error during database initialization: {e}")
        db.rollback()
        raise
    finally:
        db.close()
    
    print("\nDatabase initialization completed successfully!")
    print("\nDemo credentials:")
    print("  Admin:    username=admin,         password=admin")
    print("  Trainer:  username=sarah_johnson, password=trainer")
    print("  Customer: username=customer,      password=customer")

if __name__ == "__main__":
    init_database()
