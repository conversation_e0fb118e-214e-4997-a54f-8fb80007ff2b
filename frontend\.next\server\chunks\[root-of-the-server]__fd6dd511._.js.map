{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/api/subscriptions/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nconst SUBSCRIPTION_SERVICE_URL = process.env.SUBSCRIPTION_SERVICE_URL || 'http://localhost:8003';\n\nexport async function GET(request) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const userId = searchParams.get('user_id');\n    const trainerId = searchParams.get('trainer_id');\n    const pending = searchParams.get('pending') === 'true';\n    \n    let endpoint = '/subscriptions/';\n    \n    if (pending) {\n      endpoint = '/subscriptions/pending';\n    } else if (userId) {\n      endpoint = `/subscriptions/user/${userId}`;\n    } else if (trainerId) {\n      endpoint = `/subscriptions/trainer/${trainerId}`;\n    }\n    \n    // Forward request to Subscription Service\n    const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return NextResponse.json(\n        { message: data.detail || 'Failed to fetch subscriptions' },\n        { status: response.status }\n      );\n    }\n\n    return NextResponse.json(data);\n\n  } catch (error) {\n    console.error('Subscriptions fetch error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request) {\n  try {\n    const body = await request.json();\n    const { action, ...data } = body;\n    \n    let endpoint = '/subscriptions/subscribe';\n    let method = 'POST';\n    \n    // Handle different subscription actions\n    if (action === 'assign_trainer') {\n      endpoint = '/subscriptions/assign-trainer';\n      method = 'POST';\n    }\n    \n    // Forward request to Subscription Service\n    const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {\n      method: method,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const responseData = await response.json();\n\n    if (!response.ok) {\n      return NextResponse.json(\n        { message: responseData.detail || 'Subscription operation failed' },\n        { status: response.status }\n      );\n    }\n\n    return NextResponse.json(responseData);\n\n  } catch (error) {\n    console.error('Subscription operation error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,2BAA2B,QAAQ,GAAG,CAAC,wBAAwB,IAAI;AAElE,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC,eAAe;QAEhD,IAAI,WAAW;QAEf,IAAI,SAAS;YACX,WAAW;QACb,OAAO,IAAI,QAAQ;YACjB,WAAW,CAAC,oBAAoB,EAAE,QAAQ;QAC5C,OAAO,IAAI,WAAW;YACpB,WAAW,CAAC,uBAAuB,EAAE,WAAW;QAClD;QAEA,0CAA0C;QAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,2BAA2B,UAAU,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,KAAK,MAAM,IAAI;YAAgC,GAC1D;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG;QAE5B,IAAI,WAAW;QACf,IAAI,SAAS;QAEb,wCAAwC;QACxC,IAAI,WAAW,kBAAkB;YAC/B,WAAW;YACX,SAAS;QACX;QAEA,0CAA0C;QAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,2BAA2B,UAAU,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,aAAa,MAAM,IAAI;YAAgC,GAClE;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}