{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/api/subscriptions/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nconst SUBSCRIPTION_SERVICE_URL = process.env.SUBSCRIPTION_SERVICE_URL || 'http://localhost:8003';\n\nexport async function GET(request) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const userId = searchParams.get('user_id');\n    const trainerId = searchParams.get('trainer_id');\n    const pending = searchParams.get('pending') === 'true';\n\n    let endpoint = '/subscriptions/';\n\n    if (pending) {\n      endpoint = '/subscriptions/pending';\n    } else if (userId) {\n      endpoint = `/subscriptions/user/${userId}`;\n    } else if (trainerId) {\n      endpoint = `/subscriptions/trainer/${trainerId}`;\n    }\n\n    try {\n      // Forward request to Subscription Service\n      const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.detail || 'Failed to fetch subscriptions');\n      }\n\n      return NextResponse.json(data);\n    } catch (fetchError) {\n      console.log('Subscription service not available, returning mock data');\n\n      // Return mock data when service is not available\n      if (pending) {\n        const mockPendingSubscriptions = [\n          {\n            id: 1,\n            user_id: 101,\n            user_name: \"John Smith\",\n            plan_id: 'premium',\n            plan_name: 'Premium Training Plan',\n            payment_amount: 59.99,\n            status: 'pending',\n            subscribed_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n            trainer_id: null\n          },\n          {\n            id: 2,\n            user_id: 102,\n            user_name: \"Sarah Wilson\",\n            plan_id: 'basic',\n            plan_name: 'Basic Fitness Plan',\n            payment_amount: 29.99,\n            status: 'pending',\n            subscribed_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago\n            trainer_id: null\n          },\n          {\n            id: 3,\n            user_id: 103,\n            user_name: \"Mike Johnson\",\n            plan_id: 'premium',\n            plan_name: 'Premium Training Plan',\n            payment_amount: 59.99,\n            status: 'pending',\n            subscribed_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago\n            trainer_id: null\n          }\n        ];\n        return NextResponse.json(mockPendingSubscriptions);\n      } else if (userId) {\n        const mockUserSubscription = [\n          {\n            id: 1,\n            user_id: parseInt(userId),\n            plan_id: 'premium',\n            plan_name: 'Premium Training Plan',\n            payment_amount: 59.99,\n            status: 'active',\n            trainer_id: 1,\n            trainer_name: 'Sarah Johnson',\n            subscribed_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago\n            next_session: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString() // 2 hours from now\n          }\n        ];\n        return NextResponse.json(mockUserSubscription);\n      } else if (trainerId) {\n        const mockTrainerClients = [\n          {\n            id: 1,\n            user_id: 101,\n            user_name: \"John Smith\",\n            plan_id: 'premium',\n            plan_name: 'Premium Training Plan',\n            payment_amount: 59.99,\n            status: 'active',\n            trainer_id: parseInt(trainerId),\n            subscribed_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 2 weeks ago\n            last_session: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago\n            next_session: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString() // tomorrow\n          },\n          {\n            id: 2,\n            user_id: 104,\n            user_name: \"Emma Davis\",\n            plan_id: 'basic',\n            plan_name: 'Basic Fitness Plan',\n            payment_amount: 29.99,\n            status: 'active',\n            trainer_id: parseInt(trainerId),\n            subscribed_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago\n            last_session: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // yesterday\n            next_session: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString() // 3 hours from now\n          }\n        ];\n        return NextResponse.json(mockTrainerClients);\n      }\n\n      return NextResponse.json([]);\n    }\n\n  } catch (error) {\n    console.error('Subscriptions fetch error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request) {\n  try {\n    const body = await request.json();\n    const { action, ...data } = body;\n\n    let endpoint = '/subscriptions/subscribe';\n    let method = 'POST';\n\n    // Handle different subscription actions\n    if (action === 'assign_trainer') {\n      endpoint = '/subscriptions/assign-trainer';\n      method = 'POST';\n    }\n\n    try {\n      // Forward request to Subscription Service\n      const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {\n        method: method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      const responseData = await response.json();\n\n      if (!response.ok) {\n        throw new Error(responseData.detail || 'Subscription operation failed');\n      }\n\n      return NextResponse.json(responseData);\n    } catch (fetchError) {\n      console.log('Subscription service not available, returning mock response');\n\n      // Return mock success response when service is not available\n      if (action === 'assign_trainer') {\n        return NextResponse.json({\n          message: 'Trainer assigned successfully (mock)',\n          subscription_id: data.subscription_id,\n          trainer_id: data.trainer_id\n        });\n      }\n\n      return NextResponse.json({\n        message: 'Subscription operation completed (mock)',\n        ...data\n      });\n    }\n\n  } catch (error) {\n    console.error('Subscription operation error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,2BAA2B,QAAQ,GAAG,CAAC,wBAAwB,IAAI;AAElE,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC,eAAe;QAEhD,IAAI,WAAW;QAEf,IAAI,SAAS;YACX,WAAW;QACb,OAAO,IAAI,QAAQ;YACjB,WAAW,CAAC,oBAAoB,EAAE,QAAQ;QAC5C,OAAO,IAAI,WAAW;YACpB,WAAW,CAAC,uBAAuB,EAAE,WAAW;QAClD;QAEA,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,2BAA2B,UAAU,EAAE;gBACrE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,MAAM,IAAI;YACjC;YAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,YAAY;YACnB,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,IAAI,SAAS;gBACX,MAAM,2BAA2B;oBAC/B;wBACE,IAAI;wBACJ,SAAS;wBACT,WAAW;wBACX,SAAS;wBACT,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBACpE,YAAY;oBACd;oBACA;wBACE,IAAI;wBACJ,SAAS;wBACT,WAAW;wBACX,SAAS;wBACT,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;wBACpE,YAAY;oBACd;oBACA;wBACE,IAAI;wBACJ,SAAS;wBACT,WAAW;wBACX,SAAS;wBACT,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;wBACzE,YAAY;oBACd;iBACD;gBACD,OAAO,4LAAY,CAAC,IAAI,CAAC;YAC3B,OAAO,IAAI,QAAQ;gBACjB,MAAM,uBAAuB;oBAC3B;wBACE,IAAI;wBACJ,SAAS,SAAS;wBAClB,SAAS;wBACT,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,YAAY;wBACZ,cAAc;wBACd,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;wBACzE,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW,GAAG,mBAAmB;oBAC3F;iBACD;gBACD,OAAO,4LAAY,CAAC,IAAI,CAAC;YAC3B,OAAO,IAAI,WAAW;gBACpB,MAAM,qBAAqB;oBACzB;wBACE,IAAI;wBACJ,SAAS;wBACT,WAAW;wBACX,SAAS;wBACT,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,YAAY,SAAS;wBACrB,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;wBAC1E,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;wBACxE,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,WAAW;oBACxF;oBACA;wBACE,IAAI;wBACJ,SAAS;wBACT,WAAW;wBACX,SAAS;wBACT,WAAW;wBACX,gBAAgB;wBAChB,QAAQ;wBACR,YAAY,SAAS;wBACrB,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;wBAC1E,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;wBACxE,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW,GAAG,mBAAmB;oBAC3F;iBACD;gBACD,OAAO,4LAAY,CAAC,IAAI,CAAC;YAC3B;YAEA,OAAO,4LAAY,CAAC,IAAI,CAAC,EAAE;QAC7B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG;QAE5B,IAAI,WAAW;QACf,IAAI,SAAS;QAEb,wCAAwC;QACxC,IAAI,WAAW,kBAAkB;YAC/B,WAAW;YACX,SAAS;QACX;QAEA,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,2BAA2B,UAAU,EAAE;gBACrE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,eAAe,MAAM,SAAS,IAAI;YAExC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,aAAa,MAAM,IAAI;YACzC;YAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;QAC3B,EAAE,OAAO,YAAY;YACnB,QAAQ,GAAG,CAAC;YAEZ,6DAA6D;YAC7D,IAAI,WAAW,kBAAkB;gBAC/B,OAAO,4LAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,iBAAiB,KAAK,eAAe;oBACrC,YAAY,KAAK,UAAU;gBAC7B;YACF;YAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,GAAG,IAAI;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}