{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/api/auth/login/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { cookies } from 'next/headers';\n\nconst BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';\n\nexport async function POST(request) {\n  try {\n    const body = await request.json();\n    const { username, password } = body;\n\n    // Validate input\n    if (!username || !password) {\n      return NextResponse.json(\n        { message: 'Username and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Forward request to FastAPI backend\n    const response = await fetch(`${BACKEND_URL}/auth/login`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ username, password }),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return NextResponse.json(\n        { message: data.detail || 'Login failed' },\n        { status: response.status }\n      );\n    }\n\n    // Set refresh token as HTTP-only cookie\n    const cookieStore = await cookies();\n    cookieStore.set('refreshToken', data.refresh_token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 7 * 24 * 60 * 60, // 7 days\n      path: '/',\n    });\n\n    // Return access token and user data (refresh token is in cookie)\n    return NextResponse.json({\n      accessToken: data.access_token,\n      user: data.user,\n    });\n\n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAExC,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAE/B,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAqC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qCAAqC;QACrC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,WAAW,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAU;YAAS;QAC5C;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,KAAK,MAAM,IAAI;YAAe,GACzC;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,wCAAwC;QACxC,MAAM,cAAc,MAAM,IAAA,wLAAO;QACjC,YAAY,GAAG,CAAC,gBAAgB,KAAK,aAAa,EAAE;YAClD,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,IAAI,KAAK,KAAK;YACtB,MAAM;QACR;QAEA,iEAAiE;QACjE,OAAO,4LAAY,CAAC,IAAI,CAAC;YACvB,aAAa,KAAK,YAAY;YAC9B,MAAM,KAAK,IAAI;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}