'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/common/Layout';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useAuth } from '@/components/auth/AuthProvider';
import { AdminRoute } from '@/components/auth/ProtectedRoute';

function AdminDashboardContent() {
  const { user } = useAuth();
  const [pendingSubscriptions, setPendingSubscriptions] = useState([]);
  const [availableTrainers, setAvailableTrainers] = useState([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeSubscriptions: 0,
    revenue: 0,
    pendingAssignments: 0
  });
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    if (user) {
      loadDashboardData();
      setupWebSocketConnection();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      // Load pending subscriptions
      const pendingResponse = await fetch('/api/subscriptions?pending=true');
      if (pendingResponse.ok) {
        const pending = await pendingResponse.json();
        setPendingSubscriptions(pending);
      }

      // Load available trainers
      const trainersResponse = await fetch('/api/subscriptions/trainers');
      if (trainersResponse.ok) {
        const trainers = await trainersResponse.json();
        setAvailableTrainers(trainers);
      }

      // Update stats
      setStats({
        totalUsers: 247,
        activeSubscriptions: 89,
        revenue: 24580,
        pendingAssignments: pendingSubscriptions.length
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocketConnection = () => {
    // In a real implementation, this would connect to the WebSocket
    // For now, we'll simulate real-time notifications
    const mockNotifications = [
      {
        id: 1,
        type: 'new_subscription',
        message: 'New subscription from John Doe',
        timestamp: new Date(),
        read: false
      }
    ];
    setNotifications(mockNotifications);
  };

  const assignTrainer = async (subscriptionId, trainerId) => {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'assign_trainer',
          subscription_id: subscriptionId,
          trainer_id: trainerId
        }),
      });

      if (response.ok) {
        // Refresh data after successful assignment
        loadDashboardData();
        alert('Trainer assigned successfully!');
      } else {
        alert('Failed to assign trainer');
      }
    } catch (error) {
      console.error('Error assigning trainer:', error);
      alert('Error assigning trainer');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Admin Dashboard
            </h1>
            <p className="text-gray-600 mt-2">
              Manage subscriptions, assign trainers, and monitor gym operations.
            </p>
          </div>

          {/* Real-time Notifications */}
          {notifications.length > 0 && (
            <div className="mb-8">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                  </svg>
                  <span className="text-blue-800 font-medium">New Notifications</span>
                </div>
                <div className="mt-2">
                  {notifications.map(notification => (
                    <div key={notification.id} className="text-blue-700">
                      {notification.message}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.totalUsers}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
                  <p className="text-2xl font-semibold text-gray-900">{stats.activeSubscriptions}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                  <p className="text-2xl font-semibold text-gray-900">${stats.revenue}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending Assignments</p>
                  <p className="text-2xl font-semibold text-gray-900">{pendingSubscriptions.length}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Pending Subscriptions */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Pending Subscriptions</h2>
                  <p className="text-gray-600 mt-1">Review and assign trainers to new subscriptions</p>
                </div>
                <div className="p-6">
                  {pendingSubscriptions.length === 0 ? (
                    <div className="text-center py-8">
                      <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                      <p className="text-gray-500">No pending subscriptions</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {pendingSubscriptions.map((subscription) => (
                        <div key={subscription.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium text-gray-900">User ID: {subscription.user_id}</h3>
                              <p className="text-sm text-gray-600">Plan ID: {subscription.plan_id}</p>
                              <p className="text-sm text-gray-600">Amount: ${subscription.payment_amount}</p>
                            </div>
                            <Badge variant="warning">Pending</Badge>
                          </div>

                          <div className="flex items-center space-x-3">
                            <select
                              className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm"
                              onChange={(e) => {
                                if (e.target.value) {
                                  assignTrainer(subscription.id, parseInt(e.target.value));
                                }
                              }}
                            >
                              <option value="">Select Trainer</option>
                              {availableTrainers.map((trainer) => (
                                <option key={trainer.id} value={trainer.id}>
                                  {trainer.specialization} - {trainer.current_clients}/{trainer.max_clients} clients
                                </option>
                              ))}
                            </select>
                            <Button size="small" variant="outline">
                              View Details
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-8">
              {/* Available Trainers */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Available Trainers</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {availableTrainers.slice(0, 4).map((trainer) => (
                      <div key={trainer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{trainer.specialization}</p>
                          <p className="text-sm text-gray-600">
                            {trainer.current_clients}/{trainer.max_clients} clients
                          </p>
                          <div className="flex items-center mt-1">
                            <svg className="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <span className="text-sm text-gray-600">{trainer.rating}</span>
                          </div>
                        </div>
                        <Badge
                          variant={trainer.current_clients < trainer.max_clients ? "success" : "warning"}
                          size="small"
                        >
                          {trainer.current_clients < trainer.max_clients ? "Available" : "Full"}
                        </Badge>
                      </div>
                    ))}
                  </div>
                  <Button fullWidth variant="outline" size="small" className="mt-4">
                    View All Trainers
                  </Button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Quick Actions</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    <Button fullWidth variant="outline">
                      Generate Reports
                    </Button>
                    <Button fullWidth variant="outline">
                      Manage Plans
                    </Button>
                    <Button fullWidth variant="outline">
                      View Analytics
                    </Button>
                    <Button fullWidth variant="outline">
                      System Settings
                    </Button>
                  </div>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">New Subscription</div>
                        <div className="text-xs text-gray-500">John Doe subscribed to Premium</div>
                      </div>
                      <span className="text-xs text-gray-400">2 min ago</span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">Trainer Assigned</div>
                        <div className="text-xs text-gray-500">Sarah assigned to new client</div>
                      </div>
                      <span className="text-xs text-gray-400">1 hour ago</span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">Payment Received</div>
                        <div className="text-xs text-gray-500">$59.99 payment processed</div>
                      </div>
                      <span className="text-xs text-gray-400">3 hours ago</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default function AdminDashboard() {
  return (
    <AdminRoute>
      <AdminDashboardContent />
    </AdminRoute>
  );
}
