{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Button.js"], "sourcesContent": ["import React from 'react';\n\nconst Button = ({ \n  children, \n  variant = 'primary', \n  size = 'medium', \n  disabled = false, \n  loading = false, \n  fullWidth = false,\n  onClick,\n  type = 'button',\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500',\n    danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500',\n    warning: 'bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500',\n    outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500',\n    admin: 'bg-purple-600 hover:bg-purple-700 text-white focus:ring-purple-500',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-1 text-xs',\n    small: 'px-3 py-1.5 text-sm',\n    medium: 'px-4 py-2 text-sm',\n    large: 'px-6 py-3 text-base',\n    'extra-large': 'px-8 py-4 text-lg',\n  };\n\n  const widthClass = fullWidth ? 'w-full' : '';\n  \n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant] || variants.primary}\n    ${sizes[size] || sizes.medium}\n    ${widthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg \n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\" \n          fill=\"none\" \n          viewBox=\"0 0 24 24\"\n        >\n          <circle \n            className=\"opacity-25\" \n            cx=\"12\" \n            cy=\"12\" \n            r=\"10\" \n            stroke=\"currentColor\" \n            strokeWidth=\"4\"\n          />\n          <path \n            className=\"opacity-75\" \n            fill=\"currentColor\" \n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\n// Flight Booking Buttons\nexport const SearchFlightButton = (props) => (\n  <Button variant=\"primary\" {...props}>Search Flight</Button>\n);\n\nexport const BookNowButton = (props) => (\n  <Button variant=\"primary\" {...props}>Book Now</Button>\n);\n\nexport const CancelButton = (props) => (\n  <Button variant=\"secondary\" {...props}>Cancel</Button>\n);\n\nexport const ViewDetailsButton = (props) => (\n  <Button variant=\"outline\" {...props}>View Details</Button>\n);\n\nexport const SkipButton = (props) => (\n  <Button variant=\"ghost\" {...props}>Skip</Button>\n);\n\n// Admin Buttons\nexport const SaveChangesButton = (props) => (\n  <Button variant=\"admin\" {...props}>Save Changes</Button>\n);\n\nexport const EditButton = (props) => (\n  <Button variant=\"outline\" {...props}>Edit</Button>\n);\n\nexport const AddOptionsButton = (props) => (\n  <Button variant=\"admin\" {...props}>+ Add Options</Button>\n);\n\n// Status Buttons\nexport const ConfirmButton = (props) => (\n  <Button variant=\"success\" {...props}>Confirm</Button>\n);\n\nexport const DeleteButton = (props) => (\n  <Button variant=\"danger\" {...props}>Delete</Button>\n);\n\nexport const PendingButton = (props) => (\n  <Button variant=\"warning\" {...props}>Pending</Button>\n);\n\n// Loading Button\nexport const LoadingButton = (props) => (\n  <Button loading {...props}>Loading...</Button>\n);\n\n// Disabled Button\nexport const DisabledButton = (props) => (\n  <Button disabled variant=\"secondary\" {...props}>Disabled</Button>\n);\n\nexport default Button;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,SAAS;QAAC,EACd,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,OAAO,EACP,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;QACP,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,aAAa,YAAY,WAAW;IAE1C,MAAM,gBAAgB,AAAC,SAEnB,OADA,aAAY,UAEZ,OADA,QAAQ,CAAC,QAAQ,IAAI,SAAS,OAAO,EAAC,UAEtC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,MAAM,EAAC,UAE5B,OADA,YAAW,UACD,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QACC,MAAM;QACN,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,yOAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;;kCAER,yOAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,yOAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA3EM;AA8EC,MAAM,qBAAqB,CAAC,sBACjC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD5B;AAIN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,mBAAmB,CAAC,sBAC/B,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAU,GAAG,KAAK;kBAAE;;;;;;OADzB;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD1B;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADhB;AAKN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAO,QAAQ;QAAC,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OADrC;uCAIE", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Navbar.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Button from '../ui/Button';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [user, setUser] = useState(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is logged in\n    const token = localStorage.getItem('accessToken');\n    const userData = localStorage.getItem('user');\n\n    if (token && userData) {\n      setIsLoggedIn(true);\n      setUser(JSON.parse(userData));\n    }\n  }, []);\n\n  const handleLogout = () => {\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('user');\n    setIsLoggedIn(false);\n    setUser(null);\n    router.push('/');\n  };\n\n  const getDashboardLink = () => {\n    if (!user) return '/';\n\n    switch (user.role) {\n      case 'admin':\n        return '/admin';\n      case 'trainer':\n        return '/trainer';\n      case 'customer':\n        return '/customer';\n      default:\n        return '/';\n    }\n  };\n\n  const navLinks = [\n    { href: '#plans', label: 'Plans' },\n    { href: '#trainers', label: 'Trainers' },\n    { href: '#testimonials', label: 'Success Stories' },\n    { href: '#contact', label: 'Contact' },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center cursor-pointer\" onClick={() => router.push('/')}>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">F</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  FitLife Gym\n                </h1>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:ml-8 md:flex md:space-x-8\">\n              {navLinks.map((link) => (\n                <a\n                  key={link.href}\n                  href={link.href}\n                  className=\"text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n                >\n                  {link.label}\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {isLoggedIn && user ? (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"hidden md:flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome,</span>\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {user.username}\n                  </span>\n                </div>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push(getDashboardLink())}\n                >\n                  Dashboard\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={handleLogout}\n                >\n                  Logout\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push('/login')}\n                >\n                  Login\n                </Button>\n\n                <Button\n                  size=\"small\"\n                  className=\"bg-yellow-400 hover:bg-yellow-500 text-black\"\n                  onClick={() => router.push('/register')}\n                >\n                  Join Now\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 p-2 rounded-md\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {!isMenuOpen ? (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  </svg>\n                ) : (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t border-gray-200\">\n            {/* Mobile Navigation Links */}\n            {navLinks.map((link) => (\n              <a\n                key={link.href}\n                href={link.href}\n                className=\"text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {link.label}\n              </a>\n            ))}\n\n            {/* Mobile User Actions */}\n            <div className=\"border-t border-gray-200 pt-4 mt-4\">\n              {isLoggedIn && user ? (\n                <div className=\"space-y-2\">\n                  <div className=\"px-3 py-2\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      Welcome, {user.username}\n                    </div>\n                    <div className=\"text-xs text-gray-600 capitalize\">\n                      {user.role}\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      router.push(getDashboardLink());\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Dashboard\n                  </button>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  <button\n                    onClick={() => {\n                      router.push('/login');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Login\n                  </button>\n                  <button\n                    onClick={() => {\n                      router.push('/register');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left bg-yellow-400 hover:bg-yellow-500 text-black block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Join Now\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,qNAAQ,EAAC;IACjC,MAAM,SAAS,IAAA,8LAAS;IAExB,IAAA,sNAAS;4BAAC;YACR,6BAA6B;YAC7B,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,SAAS,UAAU;gBACrB,cAAc;gBACd,QAAQ,KAAK,KAAK,CAAC;YACrB;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAkB;QAClD;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,yOAAC;QAAI,WAAU;;0BACb,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;oCAAiD,SAAS,IAAM,OAAO,IAAI,CAAC;8CACzF,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,yOAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAOpD,yOAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,KAAK;2CAJN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAWtB,yOAAC;4BAAI,WAAU;;gCACZ,cAAc,qBACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,yOAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;;;;;;;sDAIlB,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDACV;;;;;;;;;;;6FAKH,yOAAC;oCAAI,WAAU;;sDACb,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;8CAOL,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,yOAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,CAAC,2BACA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;yGAGvE,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,4BACC,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;wBAEZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;sCAUlB,yOAAC;4BAAI,WAAU;sCACZ,cAAc,qBACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;;oDAAoC;oDACvC,KAAK,QAAQ;;;;;;;0DAEzB,yOAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;;;;;;;kDAGd,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP;4CACA,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;yFAKH,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA/NM;;QAIW,8LAAS;;;KAJpB;uCAiOS", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Layout.js"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Navbar from './Navbar';\nimport { useAuth } from '../auth/AuthProvider';\n\nconst Layout = ({ children, showNavbar = true, className = '' }) => {\n  const { isAuthenticated } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {showNavbar && isAuthenticated && <Navbar />}\n      \n      <main className={`${className}`}>\n        {children}\n      </main>\n    </div>\n  );\n};\n\n// Dashboard Layout with sidebar\nexport const DashboardLayout = ({ \n  children, \n  title, \n  subtitle,\n  actions,\n  className = '' \n}) => {\n  return (\n    <Layout className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <div className=\"md:flex md:items-center md:justify-between\">\n            <div className=\"flex-1 min-w-0\">\n              <h1 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n                {title}\n              </h1>\n              {subtitle && (\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {subtitle}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Page Content */}\n        <div className={className}>\n          {children}\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\n// Card Layout for content sections\nexport const CardLayout = ({ \n  children, \n  title, \n  subtitle,\n  actions,\n  className = '',\n  padding = true \n}) => {\n  return (\n    <div className={`bg-white shadow rounded-lg ${className}`}>\n      {(title || subtitle || actions) && (\n        <div className={`${padding ? 'px-4 py-5 sm:px-6' : 'px-6 py-4'} border-b border-gray-200`}>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              {title && (\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                  {title}\n                </h3>\n              )}\n              {subtitle && (\n                <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                  {subtitle}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex space-x-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n      \n      <div className={padding ? 'px-4 py-5 sm:p-6' : ''}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Stats Layout for dashboard metrics\nexport const StatsLayout = ({ stats = [] }) => {\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n      {stats.map((stat, index) => (\n        <div key={index} className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                {stat.icon && (\n                  <div className={`w-8 h-8 ${stat.iconColor || 'text-gray-400'}`}>\n                    {stat.icon}\n                  </div>\n                )}\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    {stat.label}\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {stat.value}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          {stat.change && (\n            <div className=\"bg-gray-50 px-5 py-3\">\n              <div className=\"text-sm\">\n                <span className={`font-medium ${\n                  stat.changeType === 'increase' ? 'text-green-600' : \n                  stat.changeType === 'decrease' ? 'text-red-600' : \n                  'text-gray-600'\n                }`}>\n                  {stat.change}\n                </span>\n                <span className=\"text-gray-500\"> from last month</span>\n              </div>\n            </div>\n          )}\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// Grid Layout for responsive content\nexport const GridLayout = ({ \n  children, \n  cols = 1, \n  gap = 6, \n  className = '' \n}) => {\n  const gridClasses = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\n  };\n\n  const gapClasses = {\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  return (\n    <div className={`grid ${gridClasses[cols]} ${gapClasses[gap]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;QAAC,EAAE,QAAQ,EAAE,aAAa,IAAI,EAAE,YAAY,EAAE,EAAE;;IAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,mMAAO;IAEnC,qBACE,yOAAC;QAAI,WAAU;;YACZ,cAAc,iCAAmB,yOAAC,+LAAM;;;;;0BAEzC,yOAAC;gBAAK,WAAW,AAAC,GAAY,OAAV;0BACjB;;;;;;;;;;;;AAIT;GAZM;;QACwB,mMAAO;;;KAD/B;AAeC,MAAM,kBAAkB;QAAC,EAC9B,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACf;IACC,qBACE,yOAAC;QAAO,WAAU;kBAChB,cAAA,yOAAC;YAAI,WAAU;;8BAEb,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,0BACC,yOAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;4BAIN,yBACC,yOAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;8BAOT,yOAAC;oBAAI,WAAW;8BACb;;;;;;;;;;;;;;;;;AAKX;MAtCa;AAyCN,MAAM,aAAa;QAAC,EACzB,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACd,UAAU,IAAI,EACf;IACC,qBACE,yOAAC;QAAI,WAAW,AAAC,8BAAuC,OAAV;;YAC3C,CAAC,SAAS,YAAY,OAAO,mBAC5B,yOAAC;gBAAI,WAAW,AAAC,GAA8C,OAA5C,UAAU,sBAAsB,aAAY;0BAC7D,cAAA,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;;gCACE,uBACC,yOAAC;oCAAG,WAAU;8CACX;;;;;;gCAGJ,0BACC,yOAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;wBAIN,yBACC,yOAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAOX,yOAAC;gBAAI,WAAW,UAAU,qBAAqB;0BAC5C;;;;;;;;;;;;AAIT;MAvCa;AA0CN,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAE,EAAE;IACxC,qBACE,yOAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,yOAAC;gBAAgB,WAAU;;kCACzB,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,kBACR,yOAAC;wCAAI,WAAW,AAAC,WAA4C,OAAlC,KAAK,SAAS,IAAI;kDAC1C,KAAK,IAAI;;;;;;;;;;;8CAIhB,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;;0DACC,yOAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,yOAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAMpB,KAAK,MAAM,kBACV,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAK,WAAW,AAAC,eAIjB,OAHC,KAAK,UAAU,KAAK,aAAa,mBACjC,KAAK,UAAU,KAAK,aAAa,iBACjC;8CAEC,KAAK,MAAM;;;;;;8CAEd,yOAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;eAhC9B;;;;;;;;;;AAwClB;MA5Ca;AA+CN,MAAM,aAAa;QAAC,EACzB,QAAQ,EACR,OAAO,CAAC,EACR,MAAM,CAAC,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,aAAa;QACjB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,yOAAC;QAAI,WAAW,AAAC,QAA4B,OAArB,WAAW,CAAC,KAAK,EAAC,KAAsB,OAAnB,UAAU,CAAC,IAAI,EAAC,KAAa,OAAV;kBAC7D;;;;;;AAGP;MAxBa;uCA0BE", "debugId": null}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Badge.js"], "sourcesContent": ["import React from 'react';\n\nconst Badge = ({ \n  children, \n  variant = 'default', \n  size = 'default',\n  outline = false,\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800',\n    primary: 'bg-blue-100 text-blue-800',\n    secondary: 'bg-gray-100 text-gray-800',\n    admin: 'bg-purple-100 text-purple-800',\n    success: 'bg-green-100 text-green-800',\n    warning: 'bg-yellow-100 text-yellow-800',\n    danger: 'bg-red-100 text-red-800',\n    info: 'bg-blue-100 text-blue-800',\n    // Status badges\n    pending: 'bg-orange-100 text-orange-800',\n    completed: 'bg-green-100 text-green-800',\n    refunded: 'bg-red-100 text-red-800',\n    cancelled: 'bg-gray-100 text-gray-800',\n    // Flight status badges\n    onTime: 'bg-green-100 text-green-800',\n    delayed: 'bg-red-100 text-red-800',\n    boarding: 'bg-blue-100 text-blue-800',\n    departed: 'bg-purple-100 text-purple-800',\n  };\n\n  const outlineVariants = {\n    default: 'border border-gray-300 text-gray-700 bg-white',\n    primary: 'border border-blue-300 text-blue-700 bg-white',\n    secondary: 'border border-gray-300 text-gray-700 bg-white',\n    admin: 'border border-purple-300 text-purple-700 bg-white',\n    success: 'border border-green-300 text-green-700 bg-white',\n    warning: 'border border-yellow-300 text-yellow-700 bg-white',\n    danger: 'border border-red-300 text-red-700 bg-white',\n    info: 'border border-blue-300 text-blue-700 bg-white',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-0.5 text-xs',\n    small: 'px-2.5 py-0.5 text-xs',\n    default: 'px-3 py-1 text-sm',\n    large: 'px-4 py-1 text-base',\n  };\n\n  const variantClasses = outline ? outlineVariants[variant] : variants[variant];\n  \n  const badgeClasses = `\n    ${baseClasses}\n    ${variantClasses || variants.default}\n    ${sizes[size] || sizes.default}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <span className={badgeClasses} {...props}>\n      {children}\n    </span>\n  );\n};\n\n// Status indicator with dot\nexport const StatusBadge = ({ status, children, ...props }) => {\n  const statusColors = {\n    onTime: 'bg-green-500',\n    delayed: 'bg-red-500',\n    boarding: 'bg-blue-500',\n    departed: 'bg-purple-500',\n    cancelled: 'bg-gray-500',\n  };\n\n  return (\n    <Badge variant={status} {...props}>\n      <span className={`w-2 h-2 rounded-full mr-2 ${statusColors[status] || 'bg-gray-500'}`}></span>\n      {children}\n    </Badge>\n  );\n};\n\n// Number badges (like notification counts)\nexport const NumberBadge = ({ number, max = 99, ...props }) => {\n  const displayNumber = number > max ? `${max}+` : number;\n  \n  return (\n    <Badge variant=\"primary\" size=\"small\" {...props}>\n      {displayNumber}\n    </Badge>\n  );\n};\n\n// Predefined badges based on design\nexport const DefaultBadge = (props) => (\n  <Badge variant=\"default\" {...props}>Default</Badge>\n);\n\nexport const PrimaryBadge = (props) => (\n  <Badge variant=\"primary\" {...props}>Primary</Badge>\n);\n\nexport const SecondaryBadge = (props) => (\n  <Badge variant=\"secondary\" {...props}>Secondary</Badge>\n);\n\nexport const AdminBadge = (props) => (\n  <Badge variant=\"admin\" {...props}>Admin</Badge>\n);\n\n// Status Badges\nexport const PendingBadge = (props) => (\n  <Badge variant=\"pending\" {...props}>Pending</Badge>\n);\n\nexport const CompletedBadge = (props) => (\n  <Badge variant=\"completed\" {...props}>Completed</Badge>\n);\n\nexport const RefundedBadge = (props) => (\n  <Badge variant=\"refunded\" {...props}>Refunded</Badge>\n);\n\nexport const CancelledBadge = (props) => (\n  <Badge variant=\"cancelled\" {...props}>Cancelled</Badge>\n);\n\n// Flight Status Badges\nexport const OnTimeBadge = (props) => (\n  <StatusBadge status=\"onTime\" {...props}>On Time</StatusBadge>\n);\n\nexport const DelayedBadge = (props) => (\n  <StatusBadge status=\"delayed\" {...props}>Delayed</StatusBadge>\n);\n\nexport const BoardingBadge = (props) => (\n  <StatusBadge status=\"boarding\" {...props}>Boarding</StatusBadge>\n);\n\nexport const DepartedBadge = (props) => (\n  <StatusBadge status=\"departed\" {...props}>Departed</StatusBadge>\n);\n\n// Outline badges\nexport const OutlineBadge = (props) => (\n  <Badge outline {...props}>Outline</Badge>\n);\n\nexport const OutlinePrimaryBadge = (props) => (\n  <Badge outline variant=\"primary\" {...props}>Outline Primary</Badge>\n);\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,QAAQ;QAAC,EACb,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,UAAU,KAAK,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;QACN,gBAAgB;QAChB,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,uBAAuB;QACvB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,kBAAkB;QACtB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,OAAO;IACT;IAEA,MAAM,iBAAiB,UAAU,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ;IAE7E,MAAM,eAAe,AAAC,SAElB,OADA,aAAY,UAEZ,OADA,kBAAkB,SAAS,OAAO,EAAC,UAEnC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,OAAO,EAAC,UACnB,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QAAK,WAAW;QAAe,GAAG,KAAK;kBACrC;;;;;;AAGP;KA/DM;AAkEC,MAAM,cAAc;QAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;IACxD,MAAM,eAAe;QACnB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,qBACE,yOAAC;QAAM,SAAS;QAAS,GAAG,KAAK;;0BAC/B,yOAAC;gBAAK,WAAW,AAAC,6BAAkE,OAAtC,YAAY,CAAC,OAAO,IAAI;;;;;;YACrE;;;;;;;AAGP;MAfa;AAkBN,MAAM,cAAc;QAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,GAAG,OAAO;IACxD,MAAM,gBAAgB,SAAS,MAAM,AAAC,GAAM,OAAJ,KAAI,OAAK;IAEjD,qBACE,yOAAC;QAAM,SAAQ;QAAU,MAAK;QAAS,GAAG,KAAK;kBAC5C;;;;;;AAGP;MARa;AAWN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD3B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAM,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADvB;AAKN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD3B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAM,SAAQ;QAAY,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OAD3B;AAKN,MAAM,cAAc,CAAC,sBAC1B,yOAAC;QAAY,QAAO;QAAU,GAAG,KAAK;kBAAE;;;;;;OAD7B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAY,QAAO;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD9B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAY,QAAO;QAAY,GAAG,KAAK;kBAAE;;;;;;OAD/B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAY,QAAO;QAAY,GAAG,KAAK;kBAAE;;;;;;OAD/B;AAKN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADf;AAIN,MAAM,sBAAsB,CAAC,sBAClC,yOAAC;QAAM,OAAO;QAAC,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OADjC;uCAIE", "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from './AuthProvider';\n\nconst ProtectedRoute = ({ \n  children, \n  allowedRoles = [], \n  redirectTo = '/login',\n  fallback = null \n}) => {\n  const { isAuthenticated, user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push(redirectTo);\n        return;\n      }\n\n      if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {\n        // Redirect to appropriate dashboard based on user role\n        switch (user.role) {\n          case 'admin':\n            router.push('/admin');\n            break;\n          case 'staff':\n            router.push('/staff');\n            break;\n          case 'customer':\n            router.push('/customer');\n            break;\n          default:\n            router.push('/');\n        }\n        return;\n      }\n    }\n  }, [isAuthenticated, user, loading, allowedRoles, router, redirectTo]);\n\n  // Show loading state\n  if (loading) {\n    return (\n      fallback || (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading...</p>\n          </div>\n        </div>\n      )\n    );\n  }\n\n  // Don't render children if not authenticated\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  // Don't render children if user doesn't have required role\n  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {\n    return null;\n  }\n\n  return children;\n};\n\n// Higher-order component for role-based protection\nexport const withRoleProtection = (WrappedComponent, allowedRoles = []) => {\n  const ProtectedComponent = (props) => {\n    return (\n      <ProtectedRoute allowedRoles={allowedRoles}>\n        <WrappedComponent {...props} />\n      </ProtectedRoute>\n    );\n  };\n\n  ProtectedComponent.displayName = `withRoleProtection(${WrappedComponent.displayName || WrappedComponent.name})`;\n  \n  return ProtectedComponent;\n};\n\n// Specific role protection components\nexport const AdminRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['admin']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\nexport const StaffRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['staff']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\nexport const CustomerRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['customer']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\n// Multi-role protection\nexport const AdminOrStaffRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['admin', 'staff']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\n// Hook for checking permissions\nexport const usePermissions = () => {\n  const { user, isAuthenticated } = useAuth();\n\n  const hasRole = (role) => {\n    return isAuthenticated && user && user.role === role;\n  };\n\n  const hasAnyRole = (roles) => {\n    return isAuthenticated && user && roles.includes(user.role);\n  };\n\n  const isAdmin = () => hasRole('admin');\n  const isStaff = () => hasRole('staff');\n  const isCustomer = () => hasRole('customer');\n  const isAdminOrStaff = () => hasAnyRole(['admin', 'staff']);\n\n  return {\n    hasRole,\n    hasAnyRole,\n    isAdmin,\n    isStaff,\n    isCustomer,\n    isAdminOrStaff,\n    userRole: user?.role,\n  };\n};\n\nexport default ProtectedRoute;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;AACA;AACA;;;;AAJA;;;;AAMA,MAAM,iBAAiB;QAAC,EACtB,QAAQ,EACR,eAAe,EAAE,EACjB,aAAa,QAAQ,EACrB,WAAW,IAAI,EAChB;;IACC,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,mMAAO;IAClD,MAAM,SAAS,IAAA,8LAAS;IAExB,IAAA,sNAAS;oCAAC;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,aAAa,MAAM,GAAG,KAAK,QAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;oBACxE,uDAAuD;oBACvD,OAAQ,KAAK,IAAI;wBACf,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF;4BACE,OAAO,IAAI,CAAC;oBAChB;oBACA;gBACF;YACF;QACF;mCAAG;QAAC;QAAiB;QAAM;QAAS;QAAc;QAAQ;KAAW;IAErE,qBAAqB;IACrB,IAAI,SAAS;QACX,OACE,0BACE,yOAAC;YAAI,WAAU;sBACb,cAAA,yOAAC;gBAAI,WAAU;;kCACb,yOAAC;wBAAI,WAAU;;;;;;kCACf,yOAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAK5C;IAEA,6CAA6C;IAC7C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,2DAA2D;IAC3D,IAAI,aAAa,MAAM,GAAG,KAAK,QAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACxE,OAAO;IACT;IAEA,OAAO;AACT;GA7DM;;QAMuC,mMAAO;QACnC,8LAAS;;;KAPpB;AAgEC,MAAM,qBAAqB,SAAC;QAAkB,gFAAe,EAAE;IACpE,MAAM,qBAAqB,CAAC;QAC1B,qBACE,yOAAC;YAAe,cAAc;sBAC5B,cAAA,yOAAC;gBAAkB,GAAG,KAAK;;;;;;;;;;;IAGjC;IAEA,mBAAmB,WAAW,GAAG,AAAC,sBAA2E,OAAtD,iBAAiB,WAAW,IAAI,iBAAiB,IAAI,EAAC;IAE7G,OAAO;AACT;AAGO,MAAM,aAAa;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAC/C,yOAAC;QAAe,cAAc;YAAC;SAAQ;QAAG,GAAG,KAAK;kBAC/C;;;;;;;MAFQ;AAMN,MAAM,aAAa;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAC/C,yOAAC;QAAe,cAAc;YAAC;SAAQ;QAAG,GAAG,KAAK;kBAC/C;;;;;;;MAFQ;AAMN,MAAM,gBAAgB;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClD,yOAAC;QAAe,cAAc;YAAC;SAAW;QAAG,GAAG,KAAK;kBAClD;;;;;;;MAFQ;AAON,MAAM,oBAAoB;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACtD,yOAAC;QAAe,cAAc;YAAC;YAAS;SAAQ;QAAG,GAAG,KAAK;kBACxD;;;;;;;MAFQ;AAON,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,IAAA,mMAAO;IAEzC,MAAM,UAAU,CAAC;QACf,OAAO,mBAAmB,QAAQ,KAAK,IAAI,KAAK;IAClD;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,mBAAmB,QAAQ,MAAM,QAAQ,CAAC,KAAK,IAAI;IAC5D;IAEA,MAAM,UAAU,IAAM,QAAQ;IAC9B,MAAM,UAAU,IAAM,QAAQ;IAC9B,MAAM,aAAa,IAAM,QAAQ;IACjC,MAAM,iBAAiB,IAAM,WAAW;YAAC;YAAS;SAAQ;IAE1D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ,EAAE,iBAAA,2BAAA,KAAM,IAAI;IACtB;AACF;IAzBa;;QACuB,mMAAO;;;uCA0B5B", "debugId": null}}, {"offset": {"line": 1619, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/customer/page.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Layout from '@/components/common/Layout';\nimport Button from '@/components/ui/Button';\nimport Badge from '@/components/ui/Badge';\nimport { useAuth } from '@/components/auth/AuthProvider';\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\n\nfunction CustomerDashboardContent() {\n  const { user } = useAuth();\n  const [subscription, setSubscription] = useState(null);\n  const [trainer, setTrainer] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (user) {\n      loadDashboardData(user.id);\n    }\n  }, [user]);\n\n  const loadDashboardData = async (userId) => {\n    try {\n      // Load user subscriptions\n      const subResponse = await fetch(`/api/subscriptions?user_id=${userId}`);\n      if (subResponse.ok) {\n        const subscriptions = await subResponse.json();\n        const activeSubscription = subscriptions.find(sub => sub.status === 'active');\n        setSubscription(activeSubscription);\n\n        // Load trainer info if assigned\n        if (activeSubscription?.trainer_id) {\n          // In a real implementation, we'd fetch trainer details\n          setTrainer({\n            id: activeSubscription.trainer_id,\n            name: \"Sarah Johnson\",\n            specialization: \"Weight Loss & Cardio\",\n            rating: 4.9\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading your dashboard...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Welcome back, {user?.full_name || user?.username}!\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              Track your fitness journey and stay connected with your trainer.\n            </p>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Active Plan</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">\n                    {subscription ? 'Premium' : 'None'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Trainer</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">\n                    {trainer ? 'Assigned' : 'Pending'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Workouts</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">12</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Next Session</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">Today</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Left Column */}\n            <div className=\"lg:col-span-2 space-y-8\">\n              {/* Current Plan */}\n              {subscription ? (\n                <div className=\"bg-white rounded-lg shadow\">\n                  <div className=\"p-6 border-b border-gray-200\">\n                    <h2 className=\"text-xl font-semibold text-gray-900\">Current Plan</h2>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-lg font-medium\">Premium Training Plan</h3>\n                      <Badge variant=\"success\">Active</Badge>\n                    </div>\n                    <p className=\"text-gray-600 mb-4\">\n                      Complete fitness package with personal trainer, custom diet plans, and weekly progress reviews.\n                    </p>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-2xl font-bold text-gray-900\">${subscription.payment_amount}/month</span>\n                      <Button variant=\"outline\" size=\"small\">\n                        View Details\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"bg-white rounded-lg shadow\">\n                  <div className=\"p-6 text-center\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">No Active Plan</h2>\n                    <p className=\"text-gray-600 mb-6\">\n                      Subscribe to a plan to get started with your fitness journey.\n                    </p>\n                    <Button onClick={() => router.push('/#plans')}>\n                      Browse Plans\n                    </Button>\n                  </div>\n                </div>\n              )}\n\n              {/* Quick Actions */}\n              <div className=\"bg-white rounded-lg shadow\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">Quick Actions</h2>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <Button\n                      variant=\"outline\"\n                      className=\"h-20 flex flex-col items-center justify-center\"\n                      onClick={() => router.push('/customer/diet')}\n                    >\n                      <svg className=\"w-6 h-6 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\" />\n                      </svg>\n                      Diet Plan\n                    </Button>\n\n                    <Button\n                      variant=\"outline\"\n                      className=\"h-20 flex flex-col items-center justify-center\"\n                      onClick={() => router.push('/customer/exercise')}\n                    >\n                      <svg className=\"w-6 h-6 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                      </svg>\n                      Workouts\n                    </Button>\n\n                    <Button\n                      variant=\"outline\"\n                      className=\"h-20 flex flex-col items-center justify-center\"\n                      onClick={() => router.push('/customer/goals')}\n                    >\n                      <svg className=\"w-6 h-6 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n                      </svg>\n                      Goals\n                    </Button>\n\n                    <Button\n                      variant=\"outline\"\n                      className=\"h-20 flex flex-col items-center justify-center\"\n                      onClick={() => router.push('/customer/calendar')}\n                    >\n                      <svg className=\"w-6 h-6 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                      </svg>\n                      Schedule\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column */}\n            <div className=\"space-y-8\">\n              {/* Trainer Info */}\n              {trainer ? (\n                <div className=\"bg-white rounded-lg shadow\">\n                  <div className=\"p-6 border-b border-gray-200\">\n                    <h2 className=\"text-xl font-semibold text-gray-900\">Your Trainer</h2>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"flex items-center mb-4\">\n                      <div className=\"w-12 h-12 bg-gray-300 rounded-full mr-4\"></div>\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">{trainer.name}</h3>\n                        <p className=\"text-sm text-gray-600\">{trainer.specialization}</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center mb-4\">\n                      <svg className=\"w-4 h-4 text-yellow-400 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                      </svg>\n                      <span className=\"text-sm text-gray-600\">{trainer.rating} rating</span>\n                    </div>\n                    <Button fullWidth variant=\"outline\" size=\"small\">\n                      Message Trainer\n                    </Button>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"bg-white rounded-lg shadow\">\n                  <div className=\"p-6 text-center\">\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Trainer Assignment</h2>\n                    <p className=\"text-gray-600 mb-4\">\n                      Your trainer will be assigned soon. You'll be notified once the assignment is complete.\n                    </p>\n                    <Badge variant=\"warning\">Pending Assignment</Badge>\n                  </div>\n                </div>\n              )}\n\n              {/* Upcoming Sessions */}\n              <div className=\"bg-white rounded-lg shadow\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">Upcoming Sessions</h2>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">Cardio Training</p>\n                        <p className=\"text-sm text-gray-600\">Today, 2:00 PM</p>\n                      </div>\n                      <Badge variant=\"primary\" size=\"small\">Today</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">Strength Training</p>\n                        <p className=\"text-sm text-gray-600\">Tomorrow, 10:00 AM</p>\n                      </div>\n                      <Badge variant=\"secondary\" size=\"small\">Tomorrow</Badge>\n                    </div>\n                  </div>\n                  <Button fullWidth variant=\"outline\" size=\"small\" className=\"mt-4\">\n                    View All Sessions\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n}\n\nexport default function CustomerDashboard() {\n  return (\n    <ProtectedRoute allowedRoles={['customer']}>\n      <CustomerDashboardContent />\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,SAAS;;IACP,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,mMAAO;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,qNAAQ,EAAC;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qNAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qNAAQ,EAAC;IAEvC,IAAA,sNAAS;8CAAC;YACR,IAAI,MAAM;gBACR,kBAAkB,KAAK,EAAE;YAC3B;QACF;6CAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,0BAA0B;YAC1B,MAAM,cAAc,MAAM,MAAM,AAAC,8BAAoC,OAAP;YAC9D,IAAI,YAAY,EAAE,EAAE;gBAClB,MAAM,gBAAgB,MAAM,YAAY,IAAI;gBAC5C,MAAM,qBAAqB,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;gBACpE,gBAAgB;gBAEhB,gCAAgC;gBAChC,IAAI,+BAAA,yCAAA,mBAAoB,UAAU,EAAE;oBAClC,uDAAuD;oBACvD,WAAW;wBACT,IAAI,mBAAmB,UAAU;wBACjC,MAAM;wBACN,gBAAgB;wBAChB,QAAQ;oBACV;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,yOAAC,+LAAM;sBACL,cAAA,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;;;;;;sCACf,yOAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,qBACE,yOAAC,+LAAM;kBACL,cAAA,yOAAC;YAAI,WAAU;sBACb,cAAA,yOAAC;gBAAI,WAAU;;kCAEb,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAG,WAAU;;oCAAmC;oCAChC,CAAA,iBAAA,2BAAA,KAAM,SAAS,MAAI,iBAAA,2BAAA,KAAM,QAAQ;oCAAC;;;;;;;0CAEnD,yOAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAMpC,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAI,WAAU;0CACb,cAAA,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDACb,cAAA,yOAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,yOAAC;oDAAE,WAAU;8DACV,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;0CAMpC,yOAAC;gCAAI,WAAU;0CACb,cAAA,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDACb,cAAA,yOAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAChF,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,yOAAC;oDAAE,WAAU;8DACV,UAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;0CAMhC,yOAAC;gCAAI,WAAU;0CACb,cAAA,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDACb,cAAA,yOAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,yOAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;0CAK1D,yOAAC;gCAAI,WAAU;0CACb,cAAA,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDACb,cAAA,yOAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,yOAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,yOAAC;wBAAI,WAAU;;0CAEb,yOAAC;gCAAI,WAAU;;oCAEZ,6BACC,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;0DAEtD,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAI,WAAU;;0EACb,yOAAC;gEAAG,WAAU;0EAAsB;;;;;;0EACpC,yOAAC,0LAAK;gEAAC,SAAQ;0EAAU;;;;;;;;;;;;kEAE3B,yOAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,yOAAC;wDAAI,WAAU;;0EACb,yOAAC;gEAAK,WAAU;;oEAAmC;oEAAE,aAAa,cAAc;oEAAC;;;;;;;0EACjF,yOAAC,2LAAM;gEAAC,SAAQ;gEAAU,MAAK;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;6DAO7C,yOAAC;wCAAI,WAAU;kDACb,cAAA,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,yOAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,yOAAC,2LAAM;oDAAC,SAAS,IAAM,OAAO,IAAI,CAAC;8DAAY;;;;;;;;;;;;;;;;;kDAQrD,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;0DAEtD,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;;sEACb,yOAAC,2LAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC;;8EAE3B,yOAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,yOAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAIR,yOAAC,2LAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC;;8EAE3B,yOAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,yOAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAIR,yOAAC,2LAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC;;8EAE3B,yOAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,yOAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAIR,yOAAC,2LAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC;;8EAE3B,yOAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,yOAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShB,yOAAC;gCAAI,WAAU;;oCAEZ,wBACC,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;0DAEtD,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAI,WAAU;;0EACb,yOAAC;gEAAI,WAAU;;;;;;0EACf,yOAAC;;kFACC,yOAAC;wEAAG,WAAU;kFAA6B,QAAQ,IAAI;;;;;;kFACvD,yOAAC;wEAAE,WAAU;kFAAyB,QAAQ,cAAc;;;;;;;;;;;;;;;;;;kEAGhE,yOAAC;wDAAI,WAAU;;0EACb,yOAAC;gEAAI,WAAU;gEAA+B,MAAK;gEAAe,SAAQ;0EACxE,cAAA,yOAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,yOAAC;gEAAK,WAAU;;oEAAyB,QAAQ,MAAM;oEAAC;;;;;;;;;;;;;kEAE1D,yOAAC,2LAAM;wDAAC,SAAS;wDAAC,SAAQ;wDAAU,MAAK;kEAAQ;;;;;;;;;;;;;;;;;6DAMrD,yOAAC;wCAAI,WAAU;kDACb,cAAA,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,yOAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,yOAAC,0LAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;kDAM/B,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAG,WAAU;8DAAsC;;;;;;;;;;;0DAEtD,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAI,WAAU;;0EACb,yOAAC;gEAAI,WAAU;;kFACb,yOAAC;;0FACC,yOAAC;gFAAE,WAAU;0FAA4B;;;;;;0FACzC,yOAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,yOAAC,0LAAK;wEAAC,SAAQ;wEAAU,MAAK;kFAAQ;;;;;;;;;;;;0EAExC,yOAAC;gEAAI,WAAU;;kFACb,yOAAC;;0FACC,yOAAC;gFAAE,WAAU;0FAA4B;;;;;;0FACzC,yOAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,yOAAC,0LAAK;wEAAC,SAAQ;wEAAY,MAAK;kFAAQ;;;;;;;;;;;;;;;;;;kEAG5C,yOAAC,2LAAM;wDAAC,SAAS;wDAAC,SAAQ;wDAAU,MAAK;wDAAQ,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpF;GAxSS;;QACU,mMAAO;;;KADjB;AA0SM,SAAS;IACtB,qBACE,yOAAC,qMAAc;QAAC,cAAc;YAAC;SAAW;kBACxC,cAAA,yOAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}