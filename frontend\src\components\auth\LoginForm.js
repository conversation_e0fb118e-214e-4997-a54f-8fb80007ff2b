'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './AuthProvider';
import Input from '../ui/Input';
import Button from '../ui/Button';

const LoginForm = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [errors, setErrors] = useState({});
  
  const { login, loading, error, clearError, user } = useAuth();
  const router = useRouter();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }
    
    // Clear general error
    if (error) {
      clearError();
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const result = await login(formData);

    if (result.success) {
      // Small delay to ensure auth state is updated
      setTimeout(() => {
        // Redirect based on user role from auth context
        if (user && user.role) {
          switch (user.role) {
            case 'admin':
              router.push('/admin');
              break;
            case 'trainer':
              router.push('/trainer');
              break;
            case 'customer':
              router.push('/customer');
              break;
            default:
              router.push('/');
          }
        } else {
          // Fallback redirection
          router.push('/customer');
        }
      }, 200);
    }
  };

  const handleDemoLogin = async (role) => {
    const demoCredentials = {
      admin: { username: 'admin', password: 'admin' },
      trainer: { username: 'sarah_johnson', password: 'trainer' },
      customer: { username: 'customer', password: 'customer' },
    };

    setFormData(demoCredentials[role]);

    const result = await login(demoCredentials[role]);

    if (result.success) {
      // Small delay to ensure auth state is updated
      setTimeout(() => {
        router.push(`/${role}`);
      }, 200);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Multi-User Authentication System
          </p>
        </div>
        
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Input
              label="Username"
              name="username"
              type="text"
              required
              placeholder="Enter your username"
              value={formData.username}
              onChange={handleChange}
              error={errors.username}
              disabled={loading}
            />
            
            <Input
              label="Password"
              name="password"
              type="password"
              required
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleChange}
              error={errors.password}
              disabled={loading}
            />
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          <div>
            <Button
              type="submit"
              fullWidth
              loading={loading}
              disabled={loading}
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </Button>
          </div>
        </form>

        {/* Demo Login Buttons */}
        <div className="mt-8">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Demo Accounts</span>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 gap-3">
            <Button
              variant="outline"
              fullWidth
              onClick={() => handleDemoLogin('admin')}
              disabled={loading}
            >
              Login as Admin
            </Button>

            <Button
              variant="outline"
              fullWidth
              onClick={() => handleDemoLogin('trainer')}
              disabled={loading}
            >
              Login as Trainer
            </Button>

            <Button
              variant="outline"
              fullWidth
              onClick={() => handleDemoLogin('customer')}
              disabled={loading}
            >
              Login as Customer
            </Button>
          </div>
        </div>

        {/* Demo Credentials Info */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Demo Credentials:</h3>
          <div className="text-xs text-blue-700 space-y-1">
            <div><strong>Admin:</strong> username: admin, password: admin</div>
            <div><strong>Trainer:</strong> username: sarah_johnson, password: trainer</div>
            <div><strong>Customer:</strong> username: customer, password: customer</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
