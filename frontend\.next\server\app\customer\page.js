var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/customer/page.js")
R.c("server/chunks/ssr/5991a_77e64ae1._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/Documents_VS CODE_gym_frontend_src_app_83f83011._.js")
R.c("server/chunks/ssr/[root-of-the-server]__713fd2de._.js")
R.c("server/chunks/ssr/5991a_next_dist_client_components_5c824ebe._.js")
R.c("server/chunks/ssr/5991a_next_dist_client_components_builtin_forbidden_49e08efd.js")
R.c("server/chunks/ssr/5991a_next_dist_client_components_builtin_unauthorized_b16c5bbf.js")
R.c("server/chunks/ssr/5991a_next_dist_client_components_builtin_global-error_36c224f4.js")
R.c("server/chunks/ssr/5991a_next_dist_32e70573._.js")
R.c("server/chunks/ssr/[root-of-the-server]__5652db92._.js")
R.m("[project]/Documents/VS CODE/gym/frontend/.next-internal/server/app/customer/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/app-page.js?page=/customer/page { GLOBAL_ERROR_MODULE => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Documents/VS CODE/gym/frontend/src/app/layout.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Documents/VS CODE/gym/frontend/src/app/customer/page.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/app-page.js?page=/customer/page { GLOBAL_ERROR_MODULE => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/Documents/VS CODE/gym/frontend/src/app/layout.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Documents/VS CODE/gym/frontend/src/app/customer/page.js [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
