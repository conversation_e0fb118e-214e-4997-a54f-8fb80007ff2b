{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/auth/AuthProvider.js"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport Cookies from 'js-cookie';\n\nconst AuthContext = createContext();\n\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'LOGIN_START':\n      return {\n        ...state,\n        loading: true,\n        error: null,\n      };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        loading: false,\n        isAuthenticated: true,\n        user: action.payload.user,\n        accessToken: action.payload.accessToken,\n        error: null,\n      };\n    case 'LOGIN_FAILURE':\n      return {\n        ...state,\n        loading: false,\n        isAuthenticated: false,\n        user: null,\n        accessToken: null,\n        error: action.payload,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        isAuthenticated: false,\n        user: null,\n        accessToken: null,\n        error: null,\n        loading: false,\n      };\n    case 'TOKEN_REFRESH':\n      return {\n        ...state,\n        accessToken: action.payload,\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        loading: action.payload,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    default:\n      return state;\n  }\n};\n\nconst initialState = {\n  isAuthenticated: false,\n  user: null,\n  accessToken: null,\n  loading: true,\n  error: null,\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        // Try to refresh the access token using the HTTP-only cookie\n        // If this fails, it means there's no valid refresh token\n        await refreshAccessToken();\n      } catch (error) {\n        console.error('Auth check failed:', error);\n        dispatch({ type: 'SET_LOADING', payload: false });\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  const login = async (credentials) => {\n    dispatch({ type: 'LOGIN_START' });\n    \n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(credentials),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Login failed');\n      }\n\n      // Store refresh token in HTTP-only cookie (handled by API route)\n      // Store access token in memory\n      dispatch({\n        type: 'LOGIN_SUCCESS',\n        payload: {\n          user: data.user,\n          accessToken: data.accessToken,\n        },\n      });\n\n      return { success: true };\n    } catch (error) {\n      dispatch({\n        type: 'LOGIN_FAILURE',\n        payload: error.message,\n      });\n      return { success: false, error: error.message };\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n      });\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear tokens and user data\n      Cookies.remove('refreshToken');\n      dispatch({ type: 'LOGOUT' });\n    }\n  };\n\n  const refreshAccessToken = async () => {\n    try {\n      const response = await fetch('/api/auth/refresh', {\n        method: 'POST',\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error('Token refresh failed');\n      }\n\n      dispatch({\n        type: 'TOKEN_REFRESH',\n        payload: data.accessToken,\n      });\n\n      // If user data is included, update it\n      if (data.user) {\n        dispatch({\n          type: 'LOGIN_SUCCESS',\n          payload: {\n            user: data.user,\n            accessToken: data.accessToken,\n          },\n        });\n      }\n\n      dispatch({ type: 'SET_LOADING', payload: false });\n      return data.accessToken;\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      dispatch({ type: 'LOGOUT' });\n      dispatch({ type: 'SET_LOADING', payload: false });\n      throw error;\n    }\n  };\n\n  const clearError = () => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  };\n\n  const value = {\n    ...state,\n    login,\n    logout,\n    refreshAccessToken,\n    clearError,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthProvider;\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,4BAAc,IAAA,kQAAa;AAEjC,MAAM,cAAc,CAAC,OAAO;IAC1B,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;gBACT,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;gBACT,iBAAiB;gBACjB,MAAM,OAAO,OAAO,CAAC,IAAI;gBACzB,aAAa,OAAO,OAAO,CAAC,WAAW;gBACvC,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;gBACT,iBAAiB;gBACjB,MAAM;gBACN,aAAa;gBACb,OAAO,OAAO,OAAO;YACvB;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,iBAAiB;gBACjB,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,SAAS;YACX;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,aAAa,OAAO,OAAO;YAC7B;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS,OAAO,OAAO;YACzB;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO;YACT;QACF;YACE,OAAO;IACX;AACF;AAEA,MAAM,eAAe;IACnB,iBAAiB;IACjB,MAAM;IACN,aAAa;IACb,SAAS;IACT,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,+PAAU,EAAC,aAAa;IAElD,sCAAsC;IACtC,IAAA,8PAAS,EAAC;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,6DAA6D;gBAC7D,yDAAyD;gBACzD,MAAM;YACR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAM;YACjD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,SAAS;YAAE,MAAM;QAAc;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;YAClC;YAEA,iEAAiE;YACjE,+BAA+B;YAC/B,SAAS;gBACP,MAAM;gBACN,SAAS;oBACP,MAAM,KAAK,IAAI;oBACf,aAAa,KAAK,WAAW;gBAC/B;YACF;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,SAAS;gBACP,MAAM;gBACN,SAAS,MAAM,OAAO;YACxB;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAC9B,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,6BAA6B;YAC7B,4MAAO,CAAC,MAAM,CAAC;YACf,SAAS;gBAAE,MAAM;YAAS;QAC5B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;YACV;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,SAAS;gBACP,MAAM;gBACN,SAAS,KAAK,WAAW;YAC3B;YAEA,sCAAsC;YACtC,IAAI,KAAK,IAAI,EAAE;gBACb,SAAS;oBACP,MAAM;oBACN,SAAS;wBACP,MAAM,KAAK,IAAI;wBACf,aAAa,KAAK,WAAW;oBAC/B;gBACF;YACF;YAEA,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;YAC/C,OAAO,KAAK,WAAW;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;gBAAE,MAAM;YAAS;YAC1B,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;YAC/C,MAAM;QACR;IACF;IAEA,MAAM,aAAa;QACjB,SAAS;YAAE,MAAM;QAAc;IACjC;IAEA,MAAM,QAAQ;QACZ,GAAG,KAAK;QACR;QACA;QACA;QACA;IACF;IAEA,qBACE,0RAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,IAAA,+PAAU,EAAC;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,2JACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,2JACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/node_modules/js-cookie/dist/js.cookie.mjs"], "sourcesContent": ["/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n"], "names": [], "mappings": "AAAA,2BAA2B,GAC3B,yBAAyB;;;;AACzB,SAAS,OAAQ,MAAM;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE;QACzB,IAAK,IAAI,OAAO,OAAQ;YACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;IACF;IACA,OAAO;AACT;AACA,wBAAwB,GAExB,yBAAyB,GACzB,IAAI,mBAAmB;IACrB,MAAM,SAAU,KAAK;QACnB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;QAC1B;QACA,OAAO,MAAM,OAAO,CAAC,oBAAoB;IAC3C;IACA,OAAO,SAAU,KAAK;QACpB,OAAO,mBAAmB,OAAO,OAAO,CACtC,4CACA;IAEJ;AACF;AACA,wBAAwB,GAExB,yBAAyB,GAEzB,SAAS,KAAM,SAAS,EAAE,iBAAiB;IACzC,SAAS,IAAK,IAAI,EAAE,KAAK,EAAE,UAAU;QACnC,IAAI,OAAO,aAAa,aAAa;YACnC;QACF;QAEA,aAAa,OAAO,CAAC,GAAG,mBAAmB;QAE3C,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAC1C,WAAW,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,OAAO,GAAG;QAClE;QACA,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,GAAG,WAAW,OAAO,CAAC,WAAW;QACrD;QAEA,OAAO,mBAAmB,MACvB,OAAO,CAAC,wBAAwB,oBAChC,OAAO,CAAC,SAAS;QAEpB,IAAI,wBAAwB;QAC5B,IAAK,IAAI,iBAAiB,WAAY;YACpC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;gBAC9B;YACF;YAEA,yBAAyB,OAAO;YAEhC,IAAI,UAAU,CAAC,cAAc,KAAK,MAAM;gBACtC;YACF;YAEA,kCAAkC;YAClC,MAAM;YACN,iEAAiE;YACjE,iBAAiB;YACjB,2DAA2D;YAC3D,iDAAiD;YACjD,MAAM;YACN,yBAAyB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACxE;QAEA,OAAQ,SAAS,MAAM,GACrB,OAAO,MAAM,UAAU,KAAK,CAAC,OAAO,QAAQ;IAChD;IAEA,SAAS,IAAK,IAAI;QAChB,IAAI,OAAO,aAAa,eAAgB,UAAU,MAAM,IAAI,CAAC,MAAO;YAClE;QACF;QAEA,mEAAmE;QACnE,uCAAuC;QACvC,IAAI,UAAU,SAAS,MAAM,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;QAChE,IAAI,MAAM,CAAC;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,QAAQ,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7B,IAAI,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;YAEhC,IAAI;gBACF,IAAI,QAAQ,mBAAmB,KAAK,CAAC,EAAE;gBACvC,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,OAAO;oBAClB;gBACF;YACF,EAAE,OAAO,GAAG,CAAC;QACf;QAEA,OAAO,OAAO,GAAG,CAAC,KAAK,GAAG;IAC5B;IAEA,OAAO,OAAO,MAAM,CAClB;QACE;QACA;QACA,QAAQ,SAAU,IAAI,EAAE,UAAU;YAChC,IACE,MACA,IACA,OAAO,CAAC,GAAG,YAAY;gBACrB,SAAS,CAAC;YACZ;QAEJ;QACA,gBAAgB,SAAU,UAAU;YAClC,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;QAC1D;QACA,eAAe,SAAU,SAAS;YAChC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,YAAY,IAAI,CAAC,UAAU;QACpE;IACF,GACA;QACE,YAAY;YAAE,OAAO,OAAO,MAAM,CAAC;QAAmB;QACtD,WAAW;YAAE,OAAO,OAAO,MAAM,CAAC;QAAW;IAC/C;AAEJ;AAEA,IAAI,MAAM,KAAK,kBAAkB;IAAE,MAAM;AAAI", "ignoreList": [0], "debugId": null}}]}