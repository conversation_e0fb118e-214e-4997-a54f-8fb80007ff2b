'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/common/Layout';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useAuth } from '@/components/auth/AuthProvider';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

function TrainerDashboardContent() {
  const { user } = useAuth();
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(null);
  const [selectedClient, setSelectedClient] = useState(null);

  useEffect(() => {
    if (user) {
      loadTrainerData(user.id);
    }
  }, [user]);

  const loadTrainerData = async (trainerId) => {
    try {
      // Load trainer's clients
      const clientsResponse = await fetch(`/api/subscriptions?trainer_id=${trainerId}`);
      if (clientsResponse.ok) {
        const clientData = await clientsResponse.json();
        setClients(clientData);
      }
    } catch (error) {
      console.error('Error loading trainer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClientAction = (action, client) => {
    setSelectedClient(client);
    setShowModal(action);
  };

  const closeModal = () => {
    setShowModal(null);
    setSelectedClient(null);
  };

  const renderModal = () => {
    if (!showModal || !selectedClient) return null;

    const modalContent = {
      progress: {
        title: `${selectedClient.user_name || `Client ${selectedClient.user_id}`} - Progress Report`,
        content: (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800">Weight Progress</h4>
                <p className="text-2xl font-bold text-blue-900">-8 lbs</p>
                <p className="text-sm text-blue-600">Starting: 180 lbs → Current: 172 lbs</p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800">Sessions Completed</h4>
                <p className="text-2xl font-bold text-green-900">12</p>
                <p className="text-sm text-green-600">This month</p>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">Recent Achievements</h4>
              <div className="space-y-2">
                <div className="flex items-center p-2 bg-yellow-50 rounded">
                  <svg className="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span className="text-sm">Increased bench press by 20 lbs</span>
                </div>
                <div className="flex items-center p-2 bg-green-50 rounded">
                  <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm">Completed 30-day consistency challenge</span>
                </div>
              </div>
            </div>
          </div>
        )
      },
      schedule: {
        title: `Schedule Session - ${selectedClient.user_name || `Client ${selectedClient.user_id}`}`,
        content: (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input
                  type="date"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                <select className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option>9:00 AM</option>
                  <option>10:00 AM</option>
                  <option>11:00 AM</option>
                  <option>2:00 PM</option>
                  <option>3:00 PM</option>
                  <option>4:00 PM</option>
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Session Type</label>
              <select className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option>Strength Training</option>
                <option>Cardio Workout</option>
                <option>Flexibility & Mobility</option>
                <option>HIIT Training</option>
                <option>Assessment Session</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="Add any specific notes or goals for this session..."
              ></textarea>
            </div>
          </div>
        )
      },
      message: {
        title: `Message ${selectedClient.user_name || `Client ${selectedClient.user_id}`}`,
        content: (
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto">
              <div className="space-y-3">
                <div className="flex justify-end">
                  <div className="bg-blue-500 text-white rounded-lg px-3 py-2 max-w-xs">
                    <p className="text-sm">Great job on today's workout! Keep up the excellent progress.</p>
                    <p className="text-xs opacity-75 mt-1">2 hours ago</p>
                  </div>
                </div>
                <div className="flex justify-start">
                  <div className="bg-white border rounded-lg px-3 py-2 max-w-xs">
                    <p className="text-sm">Thank you! I'm feeling stronger already. When is our next session?</p>
                    <p className="text-xs text-gray-500 mt-1">1 hour ago</p>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Send Message</label>
              <textarea
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="Type your message here..."
              ></textarea>
            </div>
          </div>
        )
      }
    };

    const content = modalContent[showModal];

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
          <div className="p-6 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">{content.title}</h2>
            <button
              onClick={closeModal}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div className="p-6">
            {content.content}
          </div>
          <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
            <Button onClick={closeModal} variant="outline">
              Cancel
            </Button>
            {showModal === 'schedule' && (
              <Button onClick={() => {
                alert('Session scheduled successfully!');
                closeModal();
              }}>
                Schedule Session
              </Button>
            )}
            {showModal === 'message' && (
              <Button onClick={() => {
                alert('Message sent successfully!');
                closeModal();
              }}>
                Send Message
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading trainer dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome, {user?.full_name || user?.username}!
            </h1>
            <p className="text-gray-600 mt-2">
              Manage your clients and track their fitness progress.
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Clients</p>
                  <p className="text-2xl font-semibold text-gray-900">{clients.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Sessions Today</p>
                  <p className="text-2xl font-semibold text-gray-900">3</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">This Week</p>
                  <p className="text-2xl font-semibold text-gray-900">12</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rating</p>
                  <p className="text-2xl font-semibold text-gray-900">4.9</p>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Clients */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">My Clients</h2>
                  <p className="text-gray-600 mt-1">Manage your assigned clients and their progress</p>
                </div>
                <div className="p-6">
                  {clients.length === 0 ? (
                    <div className="text-center py-8">
                      <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      <p className="text-gray-500">No clients assigned yet</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {clients.map((client) => (
                        <div key={client.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h3 className="font-medium text-gray-900">
                                {client.user_name || `Client ID: ${client.user_id}`}
                              </h3>
                              <p className="text-sm text-gray-600">
                                Plan: {client.plan_name || client.plan_id}
                              </p>
                              <p className="text-sm text-gray-600">
                                Started: {new Date(client.subscribed_at).toLocaleDateString()}
                              </p>
                              {client.next_session && (
                                <p className="text-xs text-blue-600">
                                  Next: {new Date(client.next_session).toLocaleString()}
                                </p>
                              )}
                            </div>
                            <Badge variant="success">Active</Badge>
                          </div>

                          <div className="flex items-center space-x-3">
                            <Button
                              size="small"
                              variant="outline"
                              onClick={() => handleClientAction('progress', client)}
                              className="hover:bg-blue-50 hover:border-blue-300"
                            >
                              View Progress
                            </Button>
                            <Button
                              size="small"
                              variant="outline"
                              onClick={() => handleClientAction('schedule', client)}
                              className="hover:bg-green-50 hover:border-green-300"
                            >
                              Schedule Session
                            </Button>
                            <Button
                              size="small"
                              variant="outline"
                              onClick={() => handleClientAction('message', client)}
                              className="hover:bg-purple-50 hover:border-purple-300"
                            >
                              Message Client
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-8">
              {/* Today's Schedule */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Today's Schedule</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">Morning Session</p>
                        <p className="text-sm text-gray-600">9:00 AM - 10:00 AM</p>
                      </div>
                      <Badge variant="primary" size="small">Upcoming</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">Afternoon Session</p>
                        <p className="text-sm text-gray-600">2:00 PM - 3:00 PM</p>
                      </div>
                      <Badge variant="secondary" size="small">Scheduled</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">Evening Session</p>
                        <p className="text-sm text-gray-600">6:00 PM - 7:00 PM</p>
                      </div>
                      <Badge variant="secondary" size="small">Scheduled</Badge>
                    </div>
                  </div>
                  <Button fullWidth variant="outline" size="small" className="mt-4">
                    View Full Schedule
                  </Button>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-lg shadow">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Quick Actions</h2>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    <Button
                      fullWidth
                      variant="outline"
                      onClick={() => alert('Workout Plan Creator\n\nThis feature allows you to create custom workout plans for your clients. You can:\n\n• Select exercises from our database\n• Set reps, sets, and weights\n• Add progression notes\n• Schedule workout days\n\nComing soon!')}
                    >
                      Create Workout Plan
                    </Button>
                    <Button
                      fullWidth
                      variant="outline"
                      onClick={() => alert('Diet Plan Creator\n\nThis feature allows you to create personalized nutrition plans. You can:\n\n• Set calorie targets\n• Plan meals and snacks\n• Track macronutrients\n• Add dietary restrictions\n\nComing soon!')}
                    >
                      Create Diet Plan
                    </Button>
                    <Button
                      fullWidth
                      variant="outline"
                      onClick={() => alert('Quick Schedule\n\nUse this to quickly schedule sessions with any of your clients. You can:\n\n• View available time slots\n• Send session invitations\n• Set recurring sessions\n• Add session notes\n\nComing soon!')}
                    >
                      Schedule Session
                    </Button>
                    <Button
                      fullWidth
                      variant="outline"
                      onClick={() => alert('Analytics Dashboard\n\nView detailed analytics about your training performance:\n\n• Client progress metrics\n• Session completion rates\n• Revenue tracking\n• Performance trends\n\nComing soon!')}
                    >
                      View Analytics
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Render Modal */}
      {renderModal()}
    </Layout>
  );
}

export default function TrainerDashboard() {
  return (
    <ProtectedRoute allowedRoles={['trainer']}>
      <TrainerDashboardContent />
    </ProtectedRoute>
  );
}
