{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Button.js"], "sourcesContent": ["import React from 'react';\n\nconst Button = ({ \n  children, \n  variant = 'primary', \n  size = 'medium', \n  disabled = false, \n  loading = false, \n  fullWidth = false,\n  onClick,\n  type = 'button',\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500',\n    danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500',\n    warning: 'bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500',\n    outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500',\n    admin: 'bg-purple-600 hover:bg-purple-700 text-white focus:ring-purple-500',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-1 text-xs',\n    small: 'px-3 py-1.5 text-sm',\n    medium: 'px-4 py-2 text-sm',\n    large: 'px-6 py-3 text-base',\n    'extra-large': 'px-8 py-4 text-lg',\n  };\n\n  const widthClass = fullWidth ? 'w-full' : '';\n  \n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant] || variants.primary}\n    ${sizes[size] || sizes.medium}\n    ${widthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg \n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\" \n          fill=\"none\" \n          viewBox=\"0 0 24 24\"\n        >\n          <circle \n            className=\"opacity-25\" \n            cx=\"12\" \n            cy=\"12\" \n            r=\"10\" \n            stroke=\"currentColor\" \n            strokeWidth=\"4\"\n          />\n          <path \n            className=\"opacity-75\" \n            fill=\"currentColor\" \n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\n// Flight Booking Buttons\nexport const SearchFlightButton = (props) => (\n  <Button variant=\"primary\" {...props}>Search Flight</Button>\n);\n\nexport const BookNowButton = (props) => (\n  <Button variant=\"primary\" {...props}>Book Now</Button>\n);\n\nexport const CancelButton = (props) => (\n  <Button variant=\"secondary\" {...props}>Cancel</Button>\n);\n\nexport const ViewDetailsButton = (props) => (\n  <Button variant=\"outline\" {...props}>View Details</Button>\n);\n\nexport const SkipButton = (props) => (\n  <Button variant=\"ghost\" {...props}>Skip</Button>\n);\n\n// Admin Buttons\nexport const SaveChangesButton = (props) => (\n  <Button variant=\"admin\" {...props}>Save Changes</Button>\n);\n\nexport const EditButton = (props) => (\n  <Button variant=\"outline\" {...props}>Edit</Button>\n);\n\nexport const AddOptionsButton = (props) => (\n  <Button variant=\"admin\" {...props}>+ Add Options</Button>\n);\n\n// Status Buttons\nexport const ConfirmButton = (props) => (\n  <Button variant=\"success\" {...props}>Confirm</Button>\n);\n\nexport const DeleteButton = (props) => (\n  <Button variant=\"danger\" {...props}>Delete</Button>\n);\n\nexport const PendingButton = (props) => (\n  <Button variant=\"warning\" {...props}>Pending</Button>\n);\n\n// Loading Button\nexport const LoadingButton = (props) => (\n  <Button loading {...props}>Loading...</Button>\n);\n\n// Disabled Button\nexport const DisabledButton = (props) => (\n  <Button disabled variant=\"secondary\" {...props}>Disabled</Button>\n);\n\nexport default Button;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,SAAS;QAAC,EACd,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,OAAO,EACP,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;QACP,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,aAAa,YAAY,WAAW;IAE1C,MAAM,gBAAgB,AAAC,SAEnB,OADA,aAAY,UAEZ,OADA,QAAQ,CAAC,QAAQ,IAAI,SAAS,OAAO,EAAC,UAEtC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,MAAM,EAAC,UAE5B,OADA,YAAW,UACD,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QACC,MAAM;QACN,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,yOAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;;kCAER,yOAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,yOAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA3EM;AA8EC,MAAM,qBAAqB,CAAC,sBACjC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD5B;AAIN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,mBAAmB,CAAC,sBAC/B,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAU,GAAG,KAAK;kBAAE;;;;;;OADzB;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD1B;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADhB;AAKN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAO,QAAQ;QAAC,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OADrC;uCAIE", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\nimport Button from '@/components/ui/Button';\n\nconst Hero = () => {\n  const router = useRouter();\n\n  return (\n    <section className=\"relative bg-gradient-to-r from-blue-600 to-purple-700 overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-black opacity-50\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-700/90\"></div>\n      \n      {/* Content */}\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32\">\n        <div className=\"text-center\">\n          {/* Main Heading */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-extrabold text-white mb-6\">\n            <span className=\"block\">Transform Your</span>\n            <span className=\"block text-yellow-400\">Body & Mind</span>\n          </h1>\n          \n          {/* Subheading */}\n          <p className=\"text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto\">\n            Join FitLife Gym and get personalized training, nutrition plans, and expert guidance \n            to achieve your fitness goals faster than ever before.\n          </p>\n          \n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <Button\n              size=\"large\"\n              className=\"bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-8 py-4 text-lg\"\n              onClick={() => router.push('#plans')}\n            >\n              View Membership Plans\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"large\"\n              className=\"border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg\"\n              onClick={() => router.push('/register')}\n            >\n              Start Free Trial\n            </Button>\n          </div>\n          \n          {/* Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-400 mb-2\">500+</div>\n              <div className=\"text-gray-200\">Happy Members</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-400 mb-2\">50+</div>\n              <div className=\"text-gray-200\">Expert Trainers</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-400 mb-2\">24/7</div>\n              <div className=\"text-gray-200\">Gym Access</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <svg \n          className=\"w-6 h-6 text-white\" \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\" \n            strokeWidth={2} \n            d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" \n          />\n        </svg>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,OAAO;;IACX,MAAM,SAAS,IAAA,8LAAS;IAExB,qBACE,yOAAC;QAAQ,WAAU;;0BAEjB,yOAAC;gBAAI,WAAU;;;;;;0BACf,yOAAC;gBAAI,WAAU;;;;;;0BAGf,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAG,WAAU;;8CACZ,yOAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,yOAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;sCAI1C,yOAAC;4BAAE,WAAU;sCAA2D;;;;;;sCAMxE,yOAAC;4BAAI,WAAU;;8CACb,yOAAC,2LAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC;8CAC5B;;;;;;8CAGD,yOAAC,2LAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC;8CAC5B;;;;;;;;;;;;sCAMH,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,yOAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,yOAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,yOAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,yOAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMd;GA/EM;;QACW,8LAAS;;;KADpB;uCAiFS", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Badge.js"], "sourcesContent": ["import React from 'react';\n\nconst Badge = ({ \n  children, \n  variant = 'default', \n  size = 'default',\n  outline = false,\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800',\n    primary: 'bg-blue-100 text-blue-800',\n    secondary: 'bg-gray-100 text-gray-800',\n    admin: 'bg-purple-100 text-purple-800',\n    success: 'bg-green-100 text-green-800',\n    warning: 'bg-yellow-100 text-yellow-800',\n    danger: 'bg-red-100 text-red-800',\n    info: 'bg-blue-100 text-blue-800',\n    // Status badges\n    pending: 'bg-orange-100 text-orange-800',\n    completed: 'bg-green-100 text-green-800',\n    refunded: 'bg-red-100 text-red-800',\n    cancelled: 'bg-gray-100 text-gray-800',\n    // Flight status badges\n    onTime: 'bg-green-100 text-green-800',\n    delayed: 'bg-red-100 text-red-800',\n    boarding: 'bg-blue-100 text-blue-800',\n    departed: 'bg-purple-100 text-purple-800',\n  };\n\n  const outlineVariants = {\n    default: 'border border-gray-300 text-gray-700 bg-white',\n    primary: 'border border-blue-300 text-blue-700 bg-white',\n    secondary: 'border border-gray-300 text-gray-700 bg-white',\n    admin: 'border border-purple-300 text-purple-700 bg-white',\n    success: 'border border-green-300 text-green-700 bg-white',\n    warning: 'border border-yellow-300 text-yellow-700 bg-white',\n    danger: 'border border-red-300 text-red-700 bg-white',\n    info: 'border border-blue-300 text-blue-700 bg-white',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-0.5 text-xs',\n    small: 'px-2.5 py-0.5 text-xs',\n    default: 'px-3 py-1 text-sm',\n    large: 'px-4 py-1 text-base',\n  };\n\n  const variantClasses = outline ? outlineVariants[variant] : variants[variant];\n  \n  const badgeClasses = `\n    ${baseClasses}\n    ${variantClasses || variants.default}\n    ${sizes[size] || sizes.default}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <span className={badgeClasses} {...props}>\n      {children}\n    </span>\n  );\n};\n\n// Status indicator with dot\nexport const StatusBadge = ({ status, children, ...props }) => {\n  const statusColors = {\n    onTime: 'bg-green-500',\n    delayed: 'bg-red-500',\n    boarding: 'bg-blue-500',\n    departed: 'bg-purple-500',\n    cancelled: 'bg-gray-500',\n  };\n\n  return (\n    <Badge variant={status} {...props}>\n      <span className={`w-2 h-2 rounded-full mr-2 ${statusColors[status] || 'bg-gray-500'}`}></span>\n      {children}\n    </Badge>\n  );\n};\n\n// Number badges (like notification counts)\nexport const NumberBadge = ({ number, max = 99, ...props }) => {\n  const displayNumber = number > max ? `${max}+` : number;\n  \n  return (\n    <Badge variant=\"primary\" size=\"small\" {...props}>\n      {displayNumber}\n    </Badge>\n  );\n};\n\n// Predefined badges based on design\nexport const DefaultBadge = (props) => (\n  <Badge variant=\"default\" {...props}>Default</Badge>\n);\n\nexport const PrimaryBadge = (props) => (\n  <Badge variant=\"primary\" {...props}>Primary</Badge>\n);\n\nexport const SecondaryBadge = (props) => (\n  <Badge variant=\"secondary\" {...props}>Secondary</Badge>\n);\n\nexport const AdminBadge = (props) => (\n  <Badge variant=\"admin\" {...props}>Admin</Badge>\n);\n\n// Status Badges\nexport const PendingBadge = (props) => (\n  <Badge variant=\"pending\" {...props}>Pending</Badge>\n);\n\nexport const CompletedBadge = (props) => (\n  <Badge variant=\"completed\" {...props}>Completed</Badge>\n);\n\nexport const RefundedBadge = (props) => (\n  <Badge variant=\"refunded\" {...props}>Refunded</Badge>\n);\n\nexport const CancelledBadge = (props) => (\n  <Badge variant=\"cancelled\" {...props}>Cancelled</Badge>\n);\n\n// Flight Status Badges\nexport const OnTimeBadge = (props) => (\n  <StatusBadge status=\"onTime\" {...props}>On Time</StatusBadge>\n);\n\nexport const DelayedBadge = (props) => (\n  <StatusBadge status=\"delayed\" {...props}>Delayed</StatusBadge>\n);\n\nexport const BoardingBadge = (props) => (\n  <StatusBadge status=\"boarding\" {...props}>Boarding</StatusBadge>\n);\n\nexport const DepartedBadge = (props) => (\n  <StatusBadge status=\"departed\" {...props}>Departed</StatusBadge>\n);\n\n// Outline badges\nexport const OutlineBadge = (props) => (\n  <Badge outline {...props}>Outline</Badge>\n);\n\nexport const OutlinePrimaryBadge = (props) => (\n  <Badge outline variant=\"primary\" {...props}>Outline Primary</Badge>\n);\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,QAAQ;QAAC,EACb,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,UAAU,KAAK,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;QACN,gBAAgB;QAChB,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,uBAAuB;QACvB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,kBAAkB;QACtB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,OAAO;IACT;IAEA,MAAM,iBAAiB,UAAU,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ;IAE7E,MAAM,eAAe,AAAC,SAElB,OADA,aAAY,UAEZ,OADA,kBAAkB,SAAS,OAAO,EAAC,UAEnC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,OAAO,EAAC,UACnB,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QAAK,WAAW;QAAe,GAAG,KAAK;kBACrC;;;;;;AAGP;KA/DM;AAkEC,MAAM,cAAc;QAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;IACxD,MAAM,eAAe;QACnB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,qBACE,yOAAC;QAAM,SAAS;QAAS,GAAG,KAAK;;0BAC/B,yOAAC;gBAAK,WAAW,AAAC,6BAAkE,OAAtC,YAAY,CAAC,OAAO,IAAI;;;;;;YACrE;;;;;;;AAGP;MAfa;AAkBN,MAAM,cAAc;QAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,GAAG,OAAO;IACxD,MAAM,gBAAgB,SAAS,MAAM,AAAC,GAAM,OAAJ,KAAI,OAAK;IAEjD,qBACE,yOAAC;QAAM,SAAQ;QAAU,MAAK;QAAS,GAAG,KAAK;kBAC5C;;;;;;AAGP;MARa;AAWN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD3B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAM,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADvB;AAKN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD3B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAM,SAAQ;QAAY,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OAD3B;AAKN,MAAM,cAAc,CAAC,sBAC1B,yOAAC;QAAY,QAAO;QAAU,GAAG,KAAK;kBAAE;;;;;;OAD7B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAY,QAAO;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD9B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAY,QAAO;QAAY,GAAG,KAAK;kBAAE;;;;;;OAD/B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAY,QAAO;QAAY,GAAG,KAAK;kBAAE;;;;;;OAD/B;AAKN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADf;AAIN,MAAM,sBAAsB,CAAC,sBAClC,yOAAC;QAAM,OAAO;QAAC,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OADjC;uCAIE", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Button from '@/components/ui/Button';\nimport Badge from '@/components/ui/Badge';\n\nconst PlanGrid = () => {\n  const router = useRouter();\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Fetch plans from API\n    const fetchPlans = async () => {\n      try {\n        const response = await fetch('/api/plans?public=true');\n        if (response.ok) {\n          const data = await response.json();\n          // Mark the second plan as popular (or use backend data)\n          const plansWithPopular = data.map((plan, index) => ({\n            ...plan,\n            popular: index === 1, // Make second plan popular\n            duration: plan.duration_months\n          }));\n          setPlans(plansWithPopular);\n        } else {\n          console.error('Failed to fetch plans');\n          // Fallback to sample data\n          setPlans(getSamplePlans());\n        }\n      } catch (error) {\n        console.error('Error fetching plans:', error);\n        // Fallback to sample data\n        setPlans(getSamplePlans());\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchPlans();\n  }, []);\n\n  const getSamplePlans = () => [\n    {\n      id: 1,\n      name: \"Basic Fitness\",\n      price: 29.99,\n      duration_months: 1,\n      popular: false,\n      features: [\n        \"Access to gym equipment\",\n        \"Basic workout plans\",\n        \"Monthly progress tracking\",\n        \"Email support\"\n      ],\n      description: \"Perfect for beginners starting their fitness journey\"\n    },\n    {\n      id: 2,\n      name: \"Premium Training\",\n      price: 59.99,\n      duration_months: 1,\n      popular: true,\n      features: [\n        \"Everything in Basic\",\n        \"Personal trainer assignment\",\n        \"Custom diet plans\",\n        \"Weekly progress reviews\",\n        \"Priority support\"\n      ],\n      description: \"Most popular choice for serious fitness enthusiasts\"\n    },\n    {\n      id: 3,\n      name: \"Elite Transformation\",\n      price: 99.99,\n      duration_months: 1,\n      popular: false,\n      features: [\n        \"Everything in Premium\",\n        \"1-on-1 training sessions\",\n        \"Nutrition consultation\",\n        \"Daily meal planning\",\n        \"24/7 trainer support\",\n        \"Body composition analysis\"\n      ],\n      description: \"Complete transformation package with premium support\"\n    }\n  ];\n\n  const handleSubscribe = (planId) => {\n    // Check if user is logged in\n    const token = localStorage.getItem('accessToken');\n    if (!token) {\n      router.push('/login');\n      return;\n    }\n    \n    // Redirect to subscription flow\n    router.push(`/subscribe/${planId}`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        {[1, 2, 3].map((i) => (\n          <div key={i} className=\"bg-white rounded-lg shadow-lg p-6 animate-pulse\">\n            <div className=\"h-6 bg-gray-200 rounded mb-4\"></div>\n            <div className=\"h-8 bg-gray-200 rounded mb-4\"></div>\n            <div className=\"space-y-2 mb-6\">\n              {[1, 2, 3, 4].map((j) => (\n                <div key={j} className=\"h-4 bg-gray-200 rounded\"></div>\n              ))}\n            </div>\n            <div className=\"h-10 bg-gray-200 rounded\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n      {plans.map((plan) => (\n        <div\n          key={plan.id}\n          className={`relative bg-white rounded-lg shadow-lg overflow-hidden transform transition-transform hover:scale-105 ${\n            plan.popular ? 'ring-2 ring-yellow-400' : ''\n          }`}\n        >\n          {/* Popular Badge */}\n          {plan.popular && (\n            <div className=\"absolute top-0 right-0 bg-yellow-400 text-black px-3 py-1 text-sm font-bold\">\n              Most Popular\n            </div>\n          )}\n          \n          <div className=\"p-6\">\n            {/* Plan Name */}\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{plan.name}</h3>\n            \n            {/* Price */}\n            <div className=\"mb-4\">\n              <span className=\"text-4xl font-extrabold text-gray-900\">${plan.price}</span>\n              <span className=\"text-gray-600\">/month</span>\n            </div>\n            \n            {/* Description */}\n            <p className=\"text-gray-600 mb-6\">{plan.description}</p>\n            \n            {/* Features */}\n            <ul className=\"space-y-3 mb-8\">\n              {plan.features.map((feature, index) => (\n                <li key={index} className=\"flex items-center\">\n                  <svg\n                    className=\"w-5 h-5 text-green-500 mr-3\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M5 13l4 4L19 7\"\n                    />\n                  </svg>\n                  <span className=\"text-gray-700\">{feature}</span>\n                </li>\n              ))}\n            </ul>\n            \n            {/* Subscribe Button */}\n            <Button\n              fullWidth\n              size=\"large\"\n              variant={plan.popular ? 'primary' : 'outline'}\n              onClick={() => handleSubscribe(plan.id)}\n              className={plan.popular ? 'bg-yellow-400 hover:bg-yellow-500 text-black' : ''}\n            >\n              Choose Plan\n            </Button>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default PlanGrid;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,WAAW;;IACf,MAAM,SAAS,IAAA,8LAAS;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,qNAAQ,EAAC,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qNAAQ,EAAC;IAEvC,IAAA,sNAAS;8BAAC;YACR,uBAAuB;YACvB,MAAM;iDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,wDAAwD;4BACxD,MAAM,mBAAmB,KAAK,GAAG;kFAAC,CAAC,MAAM,QAAU,CAAC;wCAClD,GAAG,IAAI;wCACP,SAAS,UAAU;wCACnB,UAAU,KAAK,eAAe;oCAChC,CAAC;;4BACD,SAAS;wBACX,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,0BAA0B;4BAC1B,SAAS;wBACX;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,0BAA0B;wBAC1B,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,MAAM,iBAAiB,IAAM;YAC3B;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,SAAS;gBACT,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,SAAS;gBACT,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,SAAS;gBACT,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;YACf;SACD;IAED,MAAM,kBAAkB,CAAC;QACvB,6BAA6B;QAC7B,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,CAAC,OAAO;YACV,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,gCAAgC;QAChC,OAAO,IAAI,CAAC,AAAC,cAAoB,OAAP;IAC5B;IAEA,IAAI,SAAS;QACX,qBACE,yOAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,yOAAC;oBAAY,WAAU;;sCACrB,yOAAC;4BAAI,WAAU;;;;;;sCACf,yOAAC;4BAAI,WAAU;;;;;;sCACf,yOAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,yOAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;sCAGd,yOAAC;4BAAI,WAAU;;;;;;;mBARP;;;;;;;;;;IAalB;IAEA,qBACE,yOAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,yOAAC;gBAEC,WAAW,AAAC,yGAEX,OADC,KAAK,OAAO,GAAG,2BAA2B;;oBAI3C,KAAK,OAAO,kBACX,yOAAC;wBAAI,WAAU;kCAA8E;;;;;;kCAK/F,yOAAC;wBAAI,WAAU;;0CAEb,yOAAC;gCAAG,WAAU;0CAAyC,KAAK,IAAI;;;;;;0CAGhE,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAK,WAAU;;4CAAwC;4CAAE,KAAK,KAAK;;;;;;;kDACpE,yOAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAIlC,yOAAC;gCAAE,WAAU;0CAAsB,KAAK,WAAW;;;;;;0CAGnD,yOAAC;gCAAG,WAAU;0CACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,yOAAC;wCAAe,WAAU;;0DACxB,yOAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,SAAQ;0DAER,cAAA,yOAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;0DAGN,yOAAC;gDAAK,WAAU;0DAAiB;;;;;;;uCAd1B;;;;;;;;;;0CAoBb,yOAAC,2LAAM;gCACL,SAAS;gCACT,MAAK;gCACL,SAAS,KAAK,OAAO,GAAG,YAAY;gCACpC,SAAS,IAAM,gBAAgB,KAAK,EAAE;gCACtC,WAAW,KAAK,OAAO,GAAG,iDAAiD;0CAC5E;;;;;;;;;;;;;eAtDE,KAAK,EAAE;;;;;;;;;;AA8DtB;GArLM;;QACW,8LAAS;;;KADpB;uCAuLS", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nconst FeatureSection = () => {\n  const features = [\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      ),\n      title: \"Personal Trainers\",\n      description: \"Get matched with certified personal trainers who create customized workout plans tailored to your goals and fitness level.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n        </svg>\n      ),\n      title: \"Custom Diet Plans\",\n      description: \"Receive personalized nutrition plans designed by our nutritionists to complement your workout routine and accelerate results.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n      ),\n      title: \"Flexible Scheduling\",\n      description: \"Book training sessions and track your progress with our integrated calendar system. Train when it works for you.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n        </svg>\n      ),\n      title: \"Progress Tracking\",\n      description: \"Monitor your fitness journey with detailed analytics, goal setting, and progress reports to keep you motivated.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      ),\n      title: \"24/7 Access\",\n      description: \"Train on your schedule with round-the-clock gym access. Our facility is always open when you need it.\"\n    },\n    {\n      icon: (\n        <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n      ),\n      title: \"Community Support\",\n      description: \"Join a supportive community of fitness enthusiasts. Participate in group classes and challenges to stay motivated.\"\n    }\n  ];\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-extrabold text-gray-900 sm:text-4xl\">\n            Why Choose FitLife Gym?\n          </h2>\n          <p className=\"mt-4 text-xl text-gray-600 max-w-3xl mx-auto\">\n            We provide everything you need to achieve your fitness goals with professional guidance, \n            personalized plans, and state-of-the-art facilities.\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"relative p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group\"\n            >\n              {/* Icon */}\n              <div className=\"flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-lg mb-4 group-hover:bg-blue-700 transition-colors\">\n                {feature.icon}\n              </div>\n              \n              {/* Title */}\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {feature.title}\n              </h3>\n              \n              {/* Description */}\n              <p className=\"text-gray-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Ready to Start Your Fitness Journey?\n            </h3>\n            <p className=\"text-lg mb-6 opacity-90\">\n              Join thousands of members who have transformed their lives with FitLife Gym\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-8 py-3 rounded-lg transition-colors\">\n                Start Free Trial\n              </button>\n              <button className=\"border-2 border-white text-white hover:bg-white hover:text-blue-600 font-bold px-8 py-3 rounded-lg transition-colors\">\n                Schedule Tour\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeatureSection;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,iBAAiB;IACrB,MAAM,WAAW;QACf;YACE,oBACE,yOAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,yOAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,yOAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,yOAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,yOAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,yOAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,yOAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,yOAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,yOAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,yOAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,yOAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,yOAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,yOAAC;QAAQ,WAAU;kBACjB,cAAA,yOAAC;YAAI,WAAU;;8BAEb,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,yOAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,yOAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,yOAAC;4BAEC,WAAU;;8CAGV,yOAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAIf,yOAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAIhB,yOAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAfjB;;;;;;;;;;8BAsBX,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,yOAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAGvC,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAO,WAAU;kDAAgG;;;;;;kDAGlH,yOAAC;wCAAO,WAAU;kDAAuH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvJ;KAvHM;uCAyHS", "debugId": null}}, {"offset": {"line": 1443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Badge from '@/components/ui/Badge';\n\nconst TrainerSection = () => {\n  const trainers = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      specialization: \"Weight Loss & Cardio\",\n      experience: \"8 years\",\n      certifications: [\"NASM-CPT\", \"Nutrition Specialist\"],\n      bio: \"Specializes in helping clients achieve sustainable weight loss through personalized cardio and strength training programs.\",\n      image: \"/api/placeholder/300/300\",\n      rating: 4.9,\n      clients: 150\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      specialization: \"Strength Training & Bodybuilding\",\n      experience: \"12 years\",\n      certifications: [\"ACSM-CPT\", \"Powerlifting Coach\"],\n      bio: \"Expert in muscle building and strength development with a focus on proper form and progressive overload.\",\n      image: \"/api/placeholder/300/300\",\n      rating: 4.8,\n      clients: 200\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      specialization: \"Yoga & Flexibility\",\n      experience: \"6 years\",\n      certifications: [\"RYT-500\", \"Pilates Instructor\"],\n      bio: \"Combines traditional yoga practices with modern fitness techniques for improved flexibility and mindfulness.\",\n      image: \"/api/placeholder/300/300\",\n      rating: 4.9,\n      clients: 120\n    },\n    {\n      id: 4,\n      name: \"<PERSON>\",\n      specialization: \"HIIT & Functional Training\",\n      experience: \"10 years\",\n      certifications: [\"CrossFit Level 2\", \"TRX Certified\"],\n      bio: \"High-intensity interval training expert focused on functional movements and athletic performance.\",\n      image: \"/api/placeholder/300/300\",\n      rating: 4.7,\n      clients: 180\n    }\n  ];\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-extrabold text-gray-900 sm:text-4xl\">\n            Meet Our Expert Trainers\n          </h2>\n          <p className=\"mt-4 text-xl text-gray-600 max-w-3xl mx-auto\">\n            Our certified personal trainers are here to guide you every step of the way. \n            Each trainer brings unique expertise to help you achieve your specific goals.\n          </p>\n        </div>\n\n        {/* Trainers Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {trainers.map((trainer) => (\n            <div\n              key={trainer.id}\n              className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow\"\n            >\n              {/* Trainer Image */}\n              <div className=\"relative h-64 bg-gray-300\">\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"></div>\n                <div className=\"absolute bottom-4 left-4 text-white\">\n                  <div className=\"flex items-center space-x-1 mb-1\">\n                    <svg className=\"w-4 h-4 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                    </svg>\n                    <span className=\"text-sm font-medium\">{trainer.rating}</span>\n                  </div>\n                  <div className=\"text-xs opacity-90\">{trainer.clients} clients</div>\n                </div>\n              </div>\n\n              {/* Trainer Info */}\n              <div className=\"p-6\">\n                {/* Name and Specialization */}\n                <h3 className=\"text-xl font-bold text-gray-900 mb-1\">\n                  {trainer.name}\n                </h3>\n                <p className=\"text-blue-600 font-medium mb-3\">\n                  {trainer.specialization}\n                </p>\n\n                {/* Experience */}\n                <div className=\"flex items-center mb-3\">\n                  <svg className=\"w-4 h-4 text-gray-400 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <span className=\"text-sm text-gray-600\">{trainer.experience} experience</span>\n                </div>\n\n                {/* Certifications */}\n                <div className=\"flex flex-wrap gap-1 mb-4\">\n                  {trainer.certifications.map((cert, index) => (\n                    <Badge key={index} variant=\"secondary\" size=\"small\">\n                      {cert}\n                    </Badge>\n                  ))}\n                </div>\n\n                {/* Bio */}\n                <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">\n                  {trainer.bio}\n                </p>\n\n                {/* Action Button */}\n                <button className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\">\n                  View Profile\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Trainer Stats */}\n        <div className=\"mt-16 bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">50+</div>\n              <div className=\"text-gray-600\">Certified Trainers</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">10,000+</div>\n              <div className=\"text-gray-600\">Training Sessions</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">95%</div>\n              <div className=\"text-gray-600\">Client Satisfaction</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">24/7</div>\n              <div className=\"text-gray-600\">Support Available</div>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-12\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Ready to Meet Your Perfect Trainer?\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            Subscribe to any plan and get matched with a trainer who fits your goals and schedule.\n          </p>\n          <button className=\"bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-8 py-3 rounded-lg transition-colors\">\n            Find My Trainer\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TrainerSection;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,YAAY;YACZ,gBAAgB;gBAAC;gBAAY;aAAuB;YACpD,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,YAAY;YACZ,gBAAgB;gBAAC;gBAAY;aAAqB;YAClD,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,YAAY;YACZ,gBAAgB;gBAAC;gBAAW;aAAqB;YACjD,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,gBAAgB;YAChB,YAAY;YACZ,gBAAgB;gBAAC;gBAAoB;aAAgB;YACrD,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;QACX;KACD;IAED,qBACE,yOAAC;QAAQ,WAAU;kBACjB,cAAA,yOAAC;YAAI,WAAU;;8BAEb,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,yOAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,yOAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,yOAAC;4BAEC,WAAU;;8CAGV,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;;;;;sDACf,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;;sEACb,yOAAC;4DAAI,WAAU;4DAA0B,MAAK;4DAAe,SAAQ;sEACnE,cAAA,yOAAC;gEAAK,GAAE;;;;;;;;;;;sEAEV,yOAAC;4DAAK,WAAU;sEAAuB,QAAQ,MAAM;;;;;;;;;;;;8DAEvD,yOAAC;oDAAI,WAAU;;wDAAsB,QAAQ,OAAO;wDAAC;;;;;;;;;;;;;;;;;;;8CAKzD,yOAAC;oCAAI,WAAU;;sDAEb,yOAAC;4CAAG,WAAU;sDACX,QAAQ,IAAI;;;;;;sDAEf,yOAAC;4CAAE,WAAU;sDACV,QAAQ,cAAc;;;;;;sDAIzB,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;oDAA6B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACpF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,yOAAC;oDAAK,WAAU;;wDAAyB,QAAQ,UAAU;wDAAC;;;;;;;;;;;;;sDAI9D,yOAAC;4CAAI,WAAU;sDACZ,QAAQ,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,yOAAC,0LAAK;oDAAa,SAAQ;oDAAY,MAAK;8DACzC;mDADS;;;;;;;;;;sDAOhB,yOAAC;4CAAE,WAAU;sDACV,QAAQ,GAAG;;;;;;sDAId,yOAAC;4CAAO,WAAU;sDAAqG;;;;;;;;;;;;;2BAlDpH,QAAQ,EAAE;;;;;;;;;;8BA2DrB,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMrC,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,yOAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,yOAAC;4BAAO,WAAU;sCAAgG;;;;;;;;;;;;;;;;;;;;;;;AAO5H;KAjKM;uCAmKS", "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\nconst Testimonials = () => {\n  const [currentTestimonial, setCurrentTestimonial] = useState(0);\n\n  const testimonials = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      role: \"Marketing Manager\",\n      image: \"/api/placeholder/80/80\",\n      rating: 5,\n      text: \"FitLife Gym completely transformed my approach to fitness. My trainer <PERSON> created a personalized plan that fit my busy schedule, and I've lost 30 pounds in 6 months!\",\n      achievement: \"Lost 30 lbs in 6 months\"\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      role: \"Software Engineer\",\n      image: \"/api/placeholder/80/80\",\n      rating: 5,\n      text: \"The nutrition plans and workout routines are incredible. <PERSON> helped me build muscle and strength I never thought possible. The 24/7 access is perfect for my schedule.\",\n      achievement: \"Gained 15 lbs muscle mass\"\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      role: \"Teacher\",\n      image: \"/api/placeholder/80/80\",\n      rating: 5,\n      text: \"<PERSON>'s yoga and flexibility training has been life-changing. My back pain is gone, and I feel more energized than ever. The community here is so supportive!\",\n      achievement: \"Eliminated chronic back pain\"\n    },\n    {\n      id: 4,\n      name: \"<PERSON>\",\n      role: \"Business Owner\",\n      image: \"/api/placeholder/80/80\",\n      rating: 5,\n      text: \"The HIIT training with <PERSON> has improved my athletic performance dramatically. The progress tracking keeps me motivated, and the results speak for themselves.\",\n      achievement: \"Improved endurance by 40%\"\n    }\n  ];\n\n  const nextTestimonial = () => {\n    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n  };\n\n  const renderStars = (rating) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <svg\n        key={i}\n        className={`w-5 h-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}\n        fill=\"currentColor\"\n        viewBox=\"0 0 20 20\"\n      >\n        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n      </svg>\n    ));\n  };\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl font-extrabold text-gray-900 sm:text-4xl\">\n            Success Stories\n          </h2>\n          <p className=\"mt-4 text-xl text-gray-600 max-w-3xl mx-auto\">\n            Hear from our members who have transformed their lives with FitLife Gym. \n            Their success could be your success story too.\n          </p>\n        </div>\n\n        {/* Main Testimonial Carousel */}\n        <div className=\"relative bg-gray-50 rounded-lg p-8 mb-12\">\n          <div className=\"text-center\">\n            {/* Quote Icon */}\n            <svg className=\"w-12 h-12 text-blue-600 mx-auto mb-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"/>\n            </svg>\n\n            {/* Testimonial Text */}\n            <blockquote className=\"text-xl text-gray-700 mb-8 max-w-4xl mx-auto\">\n              \"{testimonials[currentTestimonial].text}\"\n            </blockquote>\n\n            {/* Rating */}\n            <div className=\"flex justify-center mb-4\">\n              {renderStars(testimonials[currentTestimonial].rating)}\n            </div>\n\n            {/* Author Info */}\n            <div className=\"flex items-center justify-center space-x-4\">\n              <div className=\"w-16 h-16 bg-gray-300 rounded-full\"></div>\n              <div className=\"text-left\">\n                <div className=\"font-semibold text-gray-900\">\n                  {testimonials[currentTestimonial].name}\n                </div>\n                <div className=\"text-gray-600\">\n                  {testimonials[currentTestimonial].role}\n                </div>\n                <div className=\"text-sm text-blue-600 font-medium\">\n                  {testimonials[currentTestimonial].achievement}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation Arrows */}\n          <button\n            onClick={prevTestimonial}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 transition-colors\"\n          >\n            <svg className=\"w-6 h-6 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n            </svg>\n          </button>\n          <button\n            onClick={nextTestimonial}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-lg hover:bg-gray-50 transition-colors\"\n          >\n            <svg className=\"w-6 h-6 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Testimonial Dots */}\n        <div className=\"flex justify-center space-x-2 mb-12\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-colors ${\n                index === currentTestimonial ? 'bg-blue-600' : 'bg-gray-300'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Success Stats */}\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg p-8 text-white\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">500+</div>\n              <div className=\"text-blue-100\">Success Stories</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">85%</div>\n              <div className=\"text-blue-100\">Achieve Their Goals</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">92%</div>\n              <div className=\"text-blue-100\">Continue After 1 Year</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">4.9/5</div>\n              <div className=\"text-blue-100\">Average Rating</div>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center mt-12\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Ready to Write Your Success Story?\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            Join hundreds of members who have achieved their fitness goals with FitLife Gym.\n          </p>\n          <button className=\"bg-yellow-400 hover:bg-yellow-500 text-black font-bold px-8 py-3 rounded-lg transition-colors\">\n            Start Your Journey Today\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAIA,MAAM,eAAe;;IACnB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,qNAAQ,EAAC;IAE7D,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,QAAQ;YACR,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAClE;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IACxF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,yOAAC;gBAEC,WAAW,AAAC,WAA2D,OAAjD,IAAI,SAAS,oBAAoB;gBACvD,MAAK;gBACL,SAAQ;0BAER,cAAA,yOAAC;oBAAK,GAAE;;;;;;eALH;;;;;IAQX;IAEA,qBACE,yOAAC;QAAQ,WAAU;kBACjB,cAAA,yOAAC;YAAI,WAAU;;8BAEb,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,yOAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAO9D,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;;8CAEb,yOAAC;oCAAI,WAAU;oCAAuC,MAAK;oCAAe,SAAQ;8CAChF,cAAA,yOAAC;wCAAK,GAAE;;;;;;;;;;;8CAIV,yOAAC;oCAAW,WAAU;;wCAA+C;wCACjE,YAAY,CAAC,mBAAmB,CAAC,IAAI;wCAAC;;;;;;;8CAI1C,yOAAC;oCAAI,WAAU;8CACZ,YAAY,YAAY,CAAC,mBAAmB,CAAC,MAAM;;;;;;8CAItD,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;;;;;sDACf,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,mBAAmB,CAAC,IAAI;;;;;;8DAExC,yOAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,mBAAmB,CAAC,IAAI;;;;;;8DAExC,yOAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,mBAAmB,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sCAOrD,yOAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,yOAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/E,cAAA,yOAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,yOAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,yOAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/E,cAAA,yOAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAM3E,yOAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,yOAAC;4BAEC,SAAS,IAAM,sBAAsB;4BACrC,WAAW,AAAC,0CAEX,OADC,UAAU,qBAAqB,gBAAgB;2BAH5C;;;;;;;;;;8BAUX,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,yOAAC;;kDACC,yOAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,yOAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMrC,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,yOAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,yOAAC;4BAAO,WAAU;sCAAgG;;;;;;;;;;;;;;;;;;;;;;;AAO5H;GArLM;KAAA;uCAuLS", "debugId": null}}, {"offset": {"line": 2343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nconst ContactSection = () => {\n  return (\n    <section id=\"contact\" className=\"py-16 bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Information */}\n          <div>\n            <h2 className=\"text-3xl font-extrabold mb-8\">\n              Visit FitLife Gym Today\n            </h2>\n            <p className=\"text-gray-300 mb-8 text-lg\">\n              Ready to start your fitness journey? Come visit our state-of-the-art facility \n              or get in touch with our team. We're here to help you achieve your goals.\n            </p>\n\n            {/* Contact Details */}\n            <div className=\"space-y-6\">\n              {/* Address */}\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold mb-1\">Address</h3>\n                  <p className=\"text-gray-300\">\n                    123 Fitness Street<br />\n                    Downtown District<br />\n                    City, State 12345\n                  </p>\n                </div>\n              </div>\n\n              {/* Phone */}\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold mb-1\">Phone</h3>\n                  <p className=\"text-gray-300\">(*************</p>\n                </div>\n              </div>\n\n              {/* Email */}\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold mb-1\">Email</h3>\n                  <p className=\"text-gray-300\"><EMAIL></p>\n                </div>\n              </div>\n\n              {/* Hours */}\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"w-6 h-6 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold mb-1\">Hours</h3>\n                  <div className=\"text-gray-300 space-y-1\">\n                    <p>Monday - Friday: 5:00 AM - 11:00 PM</p>\n                    <p>Saturday - Sunday: 6:00 AM - 10:00 PM</p>\n                    <p className=\"text-yellow-400 font-medium\">24/7 Access for Members</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Social Media */}\n            <div className=\"mt-8\">\n              <h3 className=\"font-semibold mb-4\">Follow Us</h3>\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"bg-gray-800 hover:bg-gray-700 p-3 rounded-lg transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"bg-gray-800 hover:bg-gray-700 p-3 rounded-lg transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"bg-gray-800 hover:bg-gray-700 p-3 rounded-lg transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"bg-gray-800 hover:bg-gray-700 p-3 rounded-lg transition-colors\">\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Form */}\n          <div className=\"bg-gray-800 rounded-lg p-8\">\n            <h3 className=\"text-2xl font-bold mb-6\">Get In Touch</h3>\n            <form className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">First Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-white placeholder-gray-400\"\n                    placeholder=\"John\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Last Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-white placeholder-gray-400\"\n                    placeholder=\"Doe\"\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Email</label>\n                <input\n                  type=\"email\"\n                  className=\"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-white placeholder-gray-400\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Phone</label>\n                <input\n                  type=\"tel\"\n                  className=\"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-white placeholder-gray-400\"\n                  placeholder=\"(*************\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Message</label>\n                <textarea\n                  rows={4}\n                  className=\"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent text-white placeholder-gray-400\"\n                  placeholder=\"Tell us about your fitness goals...\"\n                ></textarea>\n              </div>\n              \n              <button\n                type=\"submit\"\n                className=\"w-full bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-6 rounded-lg transition-colors\"\n              >\n                Send Message\n              </button>\n            </form>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContactSection;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIA,MAAM,iBAAiB;IACrB,qBACE,yOAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,yOAAC;YAAI,WAAU;sBACb,cAAA,yOAAC;gBAAI,WAAU;;kCAEb,yOAAC;;0CACC,yOAAC;gCAAG,WAAU;0CAA+B;;;;;;0CAG7C,yOAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAM1C,yOAAC;gCAAI,WAAU;;kDAEb,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;;sEACjF,yOAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,yOAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;0DAGzE,yOAAC;;kEACC,yOAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,yOAAC;wDAAE,WAAU;;4DAAgB;0EACT,yOAAC;;;;;4DAAK;0EACP,yOAAC;;;;;4DAAK;;;;;;;;;;;;;;;;;;;kDAO7B,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;;kEACC,yOAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,yOAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAKjC,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;;kEACC,yOAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,yOAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAKjC,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;;kEACC,yOAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,yOAAC;wDAAI,WAAU;;0EACb,yOAAC;0EAAE;;;;;;0EACH,yOAAC;0EAAE;;;;;;0EACH,yOAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOnD,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,yOAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,yOAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,yOAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,yOAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,yOAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,yOAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,yOAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,yOAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,yOAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,yOAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,yOAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,yOAAC;gCAAK,WAAU;;kDACd,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;;kEACC,yOAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAClD,yOAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAGhB,yOAAC;;kEACC,yOAAC;wDAAM,WAAU;kEAAiC;;;;;;kEAClD,yOAAC;wDACC,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,yOAAC;;0DACC,yOAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,yOAAC;gDACC,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,yOAAC;;0DACC,yOAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,yOAAC;gDACC,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,yOAAC;;0DACC,yOAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,yOAAC;gDACC,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,yOAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAzKM;uCA2KS", "debugId": null}}, {"offset": {"line": 3002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Navbar.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Button from '../ui/Button';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [user, setUser] = useState(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    // Check if user is logged in\n    const token = localStorage.getItem('accessToken');\n    const userData = localStorage.getItem('user');\n\n    if (token && userData) {\n      setIsLoggedIn(true);\n      setUser(JSON.parse(userData));\n    }\n  }, []);\n\n  const handleLogout = () => {\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    localStorage.removeItem('user');\n    setIsLoggedIn(false);\n    setUser(null);\n    router.push('/');\n  };\n\n  const getDashboardLink = () => {\n    if (!user) return '/';\n\n    switch (user.role) {\n      case 'admin':\n        return '/admin';\n      case 'trainer':\n        return '/trainer';\n      case 'customer':\n        return '/customer';\n      default:\n        return '/';\n    }\n  };\n\n  const navLinks = [\n    { href: '#plans', label: 'Plans' },\n    { href: '#trainers', label: 'Trainers' },\n    { href: '#testimonials', label: 'Success Stories' },\n    { href: '#contact', label: 'Contact' },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center cursor-pointer\" onClick={() => router.push('/')}>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">F</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  FitLife Gym\n                </h1>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:ml-8 md:flex md:space-x-8\">\n              {navLinks.map((link) => (\n                <a\n                  key={link.href}\n                  href={link.href}\n                  className=\"text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n                >\n                  {link.label}\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {isLoggedIn && user ? (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"hidden md:flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome,</span>\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {user.username}\n                  </span>\n                </div>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push(getDashboardLink())}\n                >\n                  Dashboard\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={handleLogout}\n                >\n                  Logout\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push('/login')}\n                >\n                  Login\n                </Button>\n\n                <Button\n                  size=\"small\"\n                  className=\"bg-yellow-400 hover:bg-yellow-500 text-black\"\n                  onClick={() => router.push('/register')}\n                >\n                  Join Now\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 p-2 rounded-md\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {!isMenuOpen ? (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  </svg>\n                ) : (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t border-gray-200\">\n            {/* Mobile Navigation Links */}\n            {navLinks.map((link) => (\n              <a\n                key={link.href}\n                href={link.href}\n                className=\"text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {link.label}\n              </a>\n            ))}\n\n            {/* Mobile User Actions */}\n            <div className=\"border-t border-gray-200 pt-4 mt-4\">\n              {isLoggedIn && user ? (\n                <div className=\"space-y-2\">\n                  <div className=\"px-3 py-2\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      Welcome, {user.username}\n                    </div>\n                    <div className=\"text-xs text-gray-600 capitalize\">\n                      {user.role}\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      router.push(getDashboardLink());\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Dashboard\n                  </button>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  <button\n                    onClick={() => {\n                      router.push('/login');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Login\n                  </button>\n                  <button\n                    onClick={() => {\n                      router.push('/register');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left bg-yellow-400 hover:bg-yellow-500 text-black block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Join Now\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,qNAAQ,EAAC;IACjC,MAAM,SAAS,IAAA,8LAAS;IAExB,IAAA,sNAAS;4BAAC;YACR,6BAA6B;YAC7B,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,SAAS,UAAU;gBACrB,cAAc;gBACd,QAAQ,KAAK,KAAK,CAAC;YACrB;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAkB;QAClD;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,yOAAC;QAAI,WAAU;;0BACb,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;oCAAiD,SAAS,IAAM,OAAO,IAAI,CAAC;8CACzF,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,yOAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAOpD,yOAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,KAAK;2CAJN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAWtB,yOAAC;4BAAI,WAAU;;gCACZ,cAAc,qBACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,yOAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;;;;;;;sDAIlB,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDACV;;;;;;;;;;;6FAKH,yOAAC;oCAAI,WAAU;;sDACb,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;8CAOL,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,yOAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,CAAC,2BACA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;yGAGvE,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,4BACC,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;wBAEZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;sCAUlB,yOAAC;4BAAI,WAAU;sCACZ,cAAc,qBACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;;oDAAoC;oDACvC,KAAK,QAAQ;;;;;;;0DAEzB,yOAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;;;;;;;kDAGd,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP;4CACA,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;yFAKH,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA/NM;;QAIW,8LAAS;;;KAJpB;uCAiOS", "debugId": null}}, {"offset": {"line": 3460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Footer.js"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\n\nconst Footer = () => {\n  const router = useRouter();\n\n  const quickLinks = [\n    { label: 'Home', href: '/' },\n    { label: 'Plans', href: '#plans' },\n    { label: 'Trainers', href: '#trainers' },\n    { label: 'About Us', href: '#about' },\n    { label: 'Contact', href: '#contact' },\n  ];\n\n  const services = [\n    { label: 'Personal Training', href: '#' },\n    { label: 'Group Classes', href: '#' },\n    { label: 'Nutrition Coaching', href: '#' },\n    { label: 'Fitness Assessment', href: '#' },\n    { label: 'Online Training', href: '#' },\n  ];\n\n  const support = [\n    { label: 'Help Center', href: '#' },\n    { label: 'Privacy Policy', href: '#' },\n    { label: 'Terms of Service', href: '#' },\n    { label: 'Membership Agreement', href: '#' },\n    { label: 'Cancellation Policy', href: '#' },\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">F</span>\n              </div>\n              <h3 className=\"text-xl font-bold\">FitLife Gym</h3>\n            </div>\n            <p className=\"text-gray-300 mb-6\">\n              Transform your body and mind with our expert trainers, personalized plans, \n              and state-of-the-art facilities. Your fitness journey starts here.\n            </p>\n            \n            {/* Social Media */}\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-white transition-colors\"\n                  >\n                    {link.label}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Services</h4>\n            <ul className=\"space-y-2\">\n              {services.map((service, index) => (\n                <li key={index}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-white transition-colors\"\n                  >\n                    {service.label}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Support</h4>\n            <ul className=\"space-y-2\">\n              {support.map((item, index) => (\n                <li key={index}>\n                  <a\n                    href={item.href}\n                    className=\"text-gray-300 hover:text-white transition-colors\"\n                  >\n                    {item.label}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Contact Info */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"flex items-center space-x-3\">\n              <svg className=\"w-5 h-5 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n              </svg>\n              <span className=\"text-gray-300\">123 Fitness Street, Downtown District</span>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <svg className=\"w-5 h-5 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n              </svg>\n              <span className=\"text-gray-300\">(*************</span>\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <svg className=\"w-5 h-5 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n              </svg>\n              <span className=\"text-gray-300\"><EMAIL></span>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © 2024 FitLife Gym. All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n              Privacy Policy\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n              Terms of Service\n            </a>\n            <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors\">\n              Cookie Policy\n            </a>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,SAAS;;IACb,MAAM,SAAS,IAAA,8LAAS;IAExB,MAAM,aAAa;QACjB;YAAE,OAAO;YAAQ,MAAM;QAAI;QAC3B;YAAE,OAAO;YAAS,MAAM;QAAS;QACjC;YAAE,OAAO;YAAY,MAAM;QAAY;QACvC;YAAE,OAAO;YAAY,MAAM;QAAS;QACpC;YAAE,OAAO;YAAW,MAAM;QAAW;KACtC;IAED,MAAM,WAAW;QACf;YAAE,OAAO;YAAqB,MAAM;QAAI;QACxC;YAAE,OAAO;YAAiB,MAAM;QAAI;QACpC;YAAE,OAAO;YAAsB,MAAM;QAAI;QACzC;YAAE,OAAO;YAAsB,MAAM;QAAI;QACzC;YAAE,OAAO;YAAmB,MAAM;QAAI;KACvC;IAED,MAAM,UAAU;QACd;YAAE,OAAO;YAAe,MAAM;QAAI;QAClC;YAAE,OAAO;YAAkB,MAAM;QAAI;QACrC;YAAE,OAAO;YAAoB,MAAM;QAAI;QACvC;YAAE,OAAO;YAAwB,MAAM;QAAI;QAC3C;YAAE,OAAO;YAAuB,MAAM;QAAI;KAC3C;IAED,qBACE,yOAAC;QAAO,WAAU;kBAChB,cAAA,yOAAC;YAAI,WAAU;;8BACb,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;sDACb,cAAA,yOAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,yOAAC;4CAAG,WAAU;sDAAoB;;;;;;;;;;;;8CAEpC,yOAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAMlC,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,yOAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,yOAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,yOAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,yOAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,yOAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,yOAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,yOAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,yOAAC;;8CACC,yOAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,yOAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,yOAAC;sDACC,cAAA,yOAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN;;;;;;;;;;;;;;;;sCAaf,yOAAC;;8CACC,yOAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,yOAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,yOAAC;sDACC,cAAA,yOAAC;gDACC,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,KAAK;;;;;;2CALT;;;;;;;;;;;;;;;;sCAaf,yOAAC;;8CACC,yOAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,yOAAC;oCAAG,WAAU;8CACX,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,yOAAC;sDACC,cAAA,yOAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN;;;;;;;;;;;;;;;;;;;;;;8BAcjB,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;;0DACjF,yOAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;0DACrE,yOAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;kDAEvE,yOAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjF,cAAA,yOAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,yOAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjF,cAAA,yOAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,yOAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMtC,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,yOAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,yOAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7F;GAtKM;;QACW,8LAAS;;;KADpB;uCAwKS", "debugId": null}}, {"offset": {"line": 4043, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/page.js"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\nimport Hero from '@/components/home/<USER>';\nimport PlanGrid from '@/components/home/<USER>';\nimport FeatureSection from '@/components/home/<USER>';\nimport TrainerSection from '@/components/home/<USER>';\nimport Testimonials from '@/components/home/<USER>';\nimport ContactSection from '@/components/home/<USER>';\nimport Navbar from '@/components/common/Navbar';\nimport Footer from '@/components/common/Footer';\n\nexport default function Home() {\n  const router = useRouter();\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <Navbar />\n      \n      {/* Main Content */}\n      <main>\n        {/* Hero Section */}\n        <Hero />\n        \n        {/* Gym Plans Section */}\n        <section id=\"plans\" className=\"py-16 bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-extrabold text-gray-900 sm:text-4xl\">\n                Choose Your Fitness Journey\n              </h2>\n              <p className=\"mt-4 text-xl text-gray-600\">\n                Select the perfect plan and get matched with a professional trainer\n              </p>\n            </div>\n            <PlanGrid />\n          </div>\n        </section>\n        \n        {/* Features Section */}\n        <FeatureSection />\n        \n        {/* Trainers Section */}\n        <TrainerSection />\n        \n        {/* Testimonials Section */}\n        <Testimonials />\n        \n        {/* Contact Section */}\n        <ContactSection />\n      </main>\n      \n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,SAAS,IAAA,8LAAS;IAExB,qBACE,yOAAC;QAAI,WAAU;;0BACb,yOAAC,+LAAM;;;;;0BAGP,yOAAC;;kCAEC,yOAAC,2LAAI;;;;;kCAGL,yOAAC;wBAAQ,IAAG;wBAAQ,WAAU;kCAC5B,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,yOAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,yOAAC,+LAAQ;;;;;;;;;;;;;;;;kCAKb,yOAAC,qMAAc;;;;;kCAGf,yOAAC,qMAAc;;;;;kCAGf,yOAAC,mMAAY;;;;;kCAGb,yOAAC,qMAAc;;;;;;;;;;;0BAGjB,yOAAC,+LAAM;;;;;;;;;;;AAGb;GA3CwB;;QACP,8LAAS;;;KADF", "debugId": null}}]}