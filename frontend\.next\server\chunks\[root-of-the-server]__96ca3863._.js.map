{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/api/auth/refresh/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { cookies } from 'next/headers';\n\nconst BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:8000';\n\nexport async function POST(request) {\n  try {\n    const cookieStore = await cookies();\n    const refreshToken = cookieStore.get('refreshToken')?.value;\n\n    if (!refreshToken) {\n      return NextResponse.json(\n        { message: 'No refresh token found' },\n        { status: 401 }\n      );\n    }\n\n    // Forward request to FastAPI backend\n    const response = await fetch(`${BACKEND_URL}/auth/refresh`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${refreshToken}`,\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      // Clear invalid refresh token\n      cookieStore.delete('refreshToken');\n      return NextResponse.json(\n        { message: data.detail || 'Token refresh failed' },\n        { status: response.status }\n      );\n    }\n\n    // Update refresh token if a new one is provided\n    if (data.refresh_token) {\n      cookieStore.set('refreshToken', data.refresh_token, {\n        httpOnly: true,\n        secure: process.env.NODE_ENV === 'production',\n        sameSite: 'strict',\n        maxAge: 7 * 24 * 60 * 60, // 7 days\n        path: '/',\n      });\n    }\n\n    // Return new access token and user data\n    return NextResponse.json({\n      accessToken: data.access_token,\n      user: data.user,\n    });\n\n  } catch (error) {\n    console.error('Token refresh error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAExC,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,cAAc,MAAM,IAAA,wLAAO;QACjC,MAAM,eAAe,YAAY,GAAG,CAAC,iBAAiB;QAEtD,IAAI,CAAC,cAAc;YACjB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAyB,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,qCAAqC;QACrC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,aAAa,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,cAAc;YAC3C;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,8BAA8B;YAC9B,YAAY,MAAM,CAAC;YACnB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,KAAK,MAAM,IAAI;YAAuB,GACjD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,gDAAgD;QAChD,IAAI,KAAK,aAAa,EAAE;YACtB,YAAY,GAAG,CAAC,gBAAgB,KAAK,aAAa,EAAE;gBAClD,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,UAAU;gBACV,QAAQ,IAAI,KAAK,KAAK;gBACtB,MAAM;YACR;QACF;QAEA,wCAAwC;QACxC,OAAO,4LAAY,CAAC,IAAI,CAAC;YACvB,aAAa,KAAK,YAAY;YAC9B,MAAM,KAAK,IAAI;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}