var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/auth/refresh/route.js")
R.c("server/chunks/5991a_next_4b34db1b._.js")
R.c("server/chunks/[root-of-the-server]__96ca3863._.js")
R.m("[project]/Documents/VS CODE/gym/frontend/.next-internal/server/app/api/auth/refresh/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Documents/VS CODE/gym/frontend/src/app/api/auth/refresh/route.js [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Documents/VS CODE/gym/frontend/src/app/api/auth/refresh/route.js [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
