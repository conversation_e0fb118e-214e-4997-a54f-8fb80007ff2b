@echo off
echo.
echo ========================================
echo  FitLife Gym - Platform Launcher
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ and try again
    pause
    exit /b 1
)

echo ✓ Python and Node.js detected
echo.

REM Ask user if they want to initialize the database
set /p init_db="Initialize database with seed data? (y/N): "
if /i "%init_db%"=="y" (
    echo.
    echo 🗄️ Initializing database...
    cd database
    python init_db.py
    if errorlevel 1 (
        echo ERROR: Database initialization failed
        pause
        exit /b 1
    )
    cd ..
    echo ✓ Database initialized successfully
    echo.
)

REM Ask user if they want to install dependencies
set /p install_deps="Install/update dependencies? (y/N): "
if /i "%install_deps%"=="y" (
    echo.
    echo 📦 Installing frontend dependencies...
    cd frontend
    call npm install
    if errorlevel 1 (
        echo ERROR: Frontend dependency installation failed
        pause
        exit /b 1
    )
    cd ..

    echo 📦 Installing backend dependencies...
    cd backend\microservices\auth_service
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Auth service dependency installation failed
        pause
        exit /b 1
    )
    cd ..\..\..

    cd backend\microservices\plan_service
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Plan service dependency installation failed
        pause
        exit /b 1
    )
    cd ..\..\..

    cd backend\microservices\subscription_service
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Subscription service dependency installation failed
        pause
        exit /b 1
    )
    cd ..\..\..

    echo ✓ All dependencies installed successfully
    echo.
)

echo 🚀 Starting FitLife Gym Platform...
echo.
echo 📍 Service URLs:
echo   Frontend:             http://localhost:3000
echo   Auth Service:         http://localhost:8000
echo   Plan Service:         http://localhost:8002
echo   Subscription Service: http://localhost:8003
echo.
echo 📚 API Documentation:
echo   Auth Service:         http://localhost:8000/docs
echo   Plan Service:         http://localhost:8002/docs
echo   Subscription Service: http://localhost:8003/docs
echo.
echo 🔑 Demo Credentials:
echo   Admin:    username=admin,         password=admin
echo   Trainer:  username=sarah_johnson, password=trainer
echo   Customer: username=customer,      password=customer
echo   Other:    username=jessica_martinez, password=password
echo.
echo Starting services in separate terminals...
echo Press any key to continue or Ctrl+C to cancel
pause >nul

REM Start Auth Service
echo Starting Auth Service...
start "FitLife Gym - Auth Service (Port 8000)" cmd /k "cd /d %~dp0backend\microservices\auth_service && python main.py"

REM Wait a moment for the service to start
timeout /t 3 /nobreak >nul

REM Start Plan Service
echo Starting Plan Service...
start "FitLife Gym - Plan Service (Port 8002)" cmd /k "cd /d %~dp0backend\microservices\plan_service && python main.py"

REM Wait a moment for the service to start
timeout /t 3 /nobreak >nul

REM Start Subscription Service
echo Starting Subscription Service...
start "FitLife Gym - Subscription Service (Port 8003)" cmd /k "cd /d %~dp0backend\microservices\subscription_service && python main.py"

REM Wait a moment for the services to start
timeout /t 5 /nobreak >nul

REM Start Frontend
echo Starting Frontend Application...
start "FitLife Gym - Frontend (Port 3000)" cmd /k "cd /d %~dp0frontend && npm run dev"

echo.
echo ✅ All services are starting up!
echo.
echo The following terminals have been opened:
echo   1. Auth Service (Port 8000)
echo   2. Plan Service (Port 8002)
echo   3. Subscription Service (Port 8003)
echo   4. Frontend Application (Port 3000)
echo.
echo 🌐 Main Application: http://localhost:3000
echo 🔧 Admin Dashboard:  http://localhost:3000/admin
echo 👤 Customer Dashboard: http://localhost:3000/customer
echo.
echo Wait a few moments for all services to fully start up.
echo You can close this window once all services are running.
echo.
pause