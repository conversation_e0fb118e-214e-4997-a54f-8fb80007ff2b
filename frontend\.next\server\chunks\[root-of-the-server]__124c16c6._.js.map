{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/api/subscriptions/trainers/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nconst AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL || 'http://localhost:8000';\n\nexport async function GET(request) {\n  try {\n    // Forward request to Auth Service to get trainers\n    const response = await fetch(`${AUTH_SERVICE_URL}/users?role=trainer`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    if (!response.ok) {\n      // If the auth service is not available, return mock data\n      const mockTrainers = [\n        {\n          id: 1,\n          specialization: \"Weight Loss & Cardio\",\n          current_clients: 8,\n          max_clients: 15,\n          rating: 4.9\n        },\n        {\n          id: 2,\n          specialization: \"Strength Training\",\n          current_clients: 12,\n          max_clients: 15,\n          rating: 4.8\n        },\n        {\n          id: 3,\n          specialization: \"Yoga & Flexibility\",\n          current_clients: 6,\n          max_clients: 12,\n          rating: 4.7\n        },\n        {\n          id: 4,\n          specialization: \"CrossFit & HIIT\",\n          current_clients: 15,\n          max_clients: 15,\n          rating: 4.9\n        }\n      ];\n      \n      return NextResponse.json(mockTrainers);\n    }\n\n    const data = await response.json();\n    \n    // Transform user data to trainer format\n    const trainers = data.map(user => ({\n      id: user.id,\n      specialization: user.full_name || user.username,\n      current_clients: Math.floor(Math.random() * 15),\n      max_clients: 15,\n      rating: (4.5 + Math.random() * 0.5).toFixed(1)\n    }));\n\n    return NextResponse.json(trainers);\n\n  } catch (error) {\n    console.error('Trainers fetch error:', error);\n    \n    // Return mock data as fallback\n    const mockTrainers = [\n      {\n        id: 1,\n        specialization: \"Weight Loss & Cardio\",\n        current_clients: 8,\n        max_clients: 15,\n        rating: 4.9\n      },\n      {\n        id: 2,\n        specialization: \"Strength Training\",\n        current_clients: 12,\n        max_clients: 15,\n        rating: 4.8\n      },\n      {\n        id: 3,\n        specialization: \"Yoga & Flexibility\",\n        current_clients: 6,\n        max_clients: 12,\n        rating: 4.7\n      },\n      {\n        id: 4,\n        specialization: \"CrossFit & HIIT\",\n        current_clients: 15,\n        max_clients: 15,\n        rating: 4.9\n      }\n    ];\n    \n    return NextResponse.json(mockTrainers);\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB,IAAI;AAElD,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,kDAAkD;QAClD,MAAM,WAAW,MAAM,MAAM,GAAG,iBAAiB,mBAAmB,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,yDAAyD;YACzD,MAAM,eAAe;gBACnB;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,iBAAiB;oBACjB,aAAa;oBACb,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,iBAAiB;oBACjB,aAAa;oBACb,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,iBAAiB;oBACjB,aAAa;oBACb,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,gBAAgB;oBAChB,iBAAiB;oBACjB,aAAa;oBACb,QAAQ;gBACV;aACD;YAED,OAAO,4LAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,wCAAwC;QACxC,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACjC,IAAI,KAAK,EAAE;gBACX,gBAAgB,KAAK,SAAS,IAAI,KAAK,QAAQ;gBAC/C,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC5C,aAAa;gBACb,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,GAAG,EAAE,OAAO,CAAC;YAC9C,CAAC;QAED,OAAO,4LAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,+BAA+B;QAC/B,MAAM,eAAe;YACnB;gBACE,IAAI;gBACJ,gBAAgB;gBAChB,iBAAiB;gBACjB,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,gBAAgB;gBAChB,iBAAiB;gBACjB,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,gBAAgB;gBAChB,iBAAiB;gBACjB,aAAa;gBACb,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,gBAAgB;gBAChB,iBAAiB;gBACjB,aAAa;gBACb,QAAQ;YACV;SACD;QAED,OAAO,4LAAY,CAAC,IAAI,CAAC;IAC3B;AACF", "debugId": null}}]}