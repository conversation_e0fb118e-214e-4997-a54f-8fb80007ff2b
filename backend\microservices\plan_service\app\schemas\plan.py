from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

class PlanBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: Decimal
    duration_months: int = 1
    features: Optional[List[str]] = []
    is_active: bool = True

class PlanCreate(PlanBase):
    pass

class PlanUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    duration_months: Optional[int] = None
    features: Optional[List[str]] = None
    is_active: Optional[bool] = None

class PlanInDB(PlanBase):
    id: int
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PlanResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    price: Decimal
    duration_months: int
    features: List[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PlanPublic(BaseModel):
    """Public plan information for the main website"""
    id: int
    name: str
    description: Optional[str] = None
    price: Decimal
    duration_months: int
    features: List[str]

    class Config:
        from_attributes = True
