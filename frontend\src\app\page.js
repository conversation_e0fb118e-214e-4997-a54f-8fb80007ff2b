'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import Hero from '@/components/home/<USER>';
import PlanGrid from '@/components/home/<USER>';
import FeatureSection from '@/components/home/<USER>';
import TrainerSection from '@/components/home/<USER>';
import Testimonials from '@/components/home/<USER>';
import ContactSection from '@/components/home/<USER>';
import Navbar from '@/components/common/Navbar';
import Footer from '@/components/common/Footer';

export default function Home() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Main Content */}
      <main>
        {/* Hero Section */}
        <Hero />
        
        {/* Gym Plans Section */}
        <section id="plans" className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Choose Your Fitness Journey
              </h2>
              <p className="mt-4 text-xl text-gray-600">
                Select the perfect plan and get matched with a professional trainer
              </p>
            </div>
            <PlanGrid />
          </div>
        </section>
        
        {/* Features Section */}
        <FeatureSection />
        
        {/* Trainers Section */}
        <TrainerSection />
        
        {/* Testimonials Section */}
        <Testimonials />
        
        {/* Contact Section */}
        <ContactSection />
      </main>
      
      <Footer />
    </div>
  );
}
