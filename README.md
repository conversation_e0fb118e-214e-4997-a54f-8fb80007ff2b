# FitLife Gym - Online Consultation Platform

A comprehensive online gym consultation website where users can subscribe to fitness plans, get assigned to trainers, and receive personalized diet, exercise, and goal plans through a calendar-based system.

## 🏋️ Features

- **Public Gym Website**: Beautiful landing page with plan subscriptions
- **Role-Based Access Control**: Customer, Trainer, and Admin dashboards
- **Microservices Architecture**: 6 independent FastAPI services
- **Real-time Notifications**: Live subscription alerts for admins
- **Trainer Assignment System**: Automatic trainer-user matching
- **Personalized Plans**: Custom diet, exercise, and goal plans
- **Calendar Integration**: Scheduling and progress tracking
- **Modern UI**: Responsive design with Tailwind CSS

## 👥 User Roles

### 🏠 Public Website (localhost:3000)
- Browse gym plans and pricing
- User registration and login
- Plan subscription with payment flow
- Gym information and contact details

### 👤 Customer Dashboard
- View assigned trainer information
- Access personalized diet plans
- Follow exercise routines
- Track goals and progress
- Calendar with scheduled activities
- Profile management

### 💪 Trainer Dashboard
- View assigned users list
- Create and manage diet plans
- Design exercise routines
- Set and track user goals
- Manage calendar schedules
- Monitor user progress

### 🔧 Admin Dashboard
- Real-time subscription notifications
- Assign trainers to new subscribers
- Manage gym plans (CRUD operations)
- View all users and subscriptions
- Trainer management
- Platform analytics

## 🎯 Demo Credentials

| Role | Username | Password |
|------|----------|----------|
| Admin | admin | admin |
| Trainer | trainer | trainer |
| Customer | customer | customer |

## 🏗️ Microservices Architecture

### Services Overview
- **Auth Service** (Port 8000): Authentication & JWT management
- **User Service** (Port 8001): User profiles & management
- **Plan Service** (Port 8002): Gym plans & pricing
- **Subscription Service** (Port 8003): Plan subscriptions & trainer assignment
- **Training Service** (Port 8004): Diet/exercise plans & goals
- **Calendar Service** (Port 8005): Scheduling & events

## 📁 Project Structure

```
gym-website/
├── frontend/                          # Next.js Application (Port 3000)
│   ├── src/
│   │   ├── app/
│   │   │   ├── page.js               # Main Gym Website
│   │   │   ├── login/page.js         # Login Page
│   │   │   ├── register/page.js      # Registration
│   │   │   ├── admin/                # Admin Dashboard
│   │   │   │   ├── page.js
│   │   │   │   ├── plans/
│   │   │   │   ├── users/
│   │   │   │   ├── subscriptions/
│   │   │   │   └── trainers/
│   │   │   ├── trainer/              # Trainer Dashboard
│   │   │   │   ├── page.js
│   │   │   │   ├── users/
│   │   │   │   └── calendar/
│   │   │   ├── customer/             # Customer Dashboard
│   │   │   │   ├── page.js
│   │   │   │   ├── plans/
│   │   │   │   ├── trainer/
│   │   │   │   ├── diet/
│   │   │   │   ├── exercise/
│   │   │   │   ├── goals/
│   │   │   │   └── calendar/
│   │   │   └── api/                  # API routes
│   │   ├── components/
│   │   │   ├── home/                 # Landing page components
│   │   │   ├── auth/                 # Authentication
│   │   │   ├── admin/                # Admin components
│   │   │   ├── trainer/              # Trainer components
│   │   │   ├── customer/             # Customer components
│   │   │   └── common/               # Shared components
│   │   └── lib/                      # Utilities
├── backend/                          # FastAPI Microservices
│   └── microservices/
│       ├── auth_service/             # Port 8000
│       ├── user_service/             # Port 8001
│       ├── plan_service/             # Port 8002
│       ├── subscription_service/     # Port 8003
│       ├── training_service/         # Port 8004
│       └── calendar_service/         # Port 8005
└── database/                         # SQLite databases
```

## Setup Instructions

### Prerequisites

- Node.js 18+ and npm
- Python 3.8+
- Git

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend will be available at http://localhost:3000

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend/microservices/auth_service
```

2. Create a virtual environment (recommended):
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Initialize the database and start the server:
```bash
python start.py
```

Or manually:
```bash
python init_db.py  # Initialize database with seed data
python main.py     # Start the FastAPI server
```

The backend will be available at http://localhost:8000
API documentation: http://localhost:8000/docs

## Environment Configuration

### Frontend (.env.local)
```env
BACKEND_URL=http://localhost:8000
```

### Backend (.env)
```env
SECRET_KEY=your-secret-key-change-this-in-production
DATABASE_URL=sqlite:///./auth.db
DEBUG=true
```

## API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user info

### Users
- `GET /users/` - List users (staff/admin only)
- `POST /users/` - Create user (admin only)
- `GET /users/{id}` - Get user by ID
- `PUT /users/{id}` - Update user
- `DELETE /users/{id}` - Delete user (admin only)

## Technology Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **Tailwind CSS** - Utility-first CSS framework
- **React Hooks** - State management
- **Axios** - HTTP client
- **js-cookie** - Cookie management

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - SQL toolkit and ORM
- **Pydantic** - Data validation
- **python-jose** - JWT token handling
- **passlib** - Password hashing
- **SQLite** - Database

## Development

### Running Tests
```bash
# Frontend
cd frontend
npm test

# Backend
cd backend/microservices/auth_service
pytest
```

### Building for Production
```bash
# Frontend
cd frontend
npm run build

# Backend
cd backend/microservices/auth_service
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## Security Features

- JWT-based authentication with access and refresh tokens
- Password hashing with bcrypt
- Role-based access control
- HTTP-only cookies for refresh tokens
- CORS protection
- Input validation and sanitization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is for educational purposes and demonstration of authentication patterns.
