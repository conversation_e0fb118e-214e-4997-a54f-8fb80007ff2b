import { NextResponse } from 'next/server';

const SUBSCRIPTION_SERVICE_URL = process.env.SUBSCRIPTION_SERVICE_URL || 'http://localhost:8003';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');
    const trainerId = searchParams.get('trainer_id');
    const pending = searchParams.get('pending') === 'true';
    
    let endpoint = '/subscriptions/';
    
    if (pending) {
      endpoint = '/subscriptions/pending';
    } else if (userId) {
      endpoint = `/subscriptions/user/${userId}`;
    } else if (trainerId) {
      endpoint = `/subscriptions/trainer/${trainerId}`;
    }
    
    // Forward request to Subscription Service
    const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { message: data.detail || 'Failed to fetch subscriptions' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Subscriptions fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action, ...data } = body;
    
    let endpoint = '/subscriptions/subscribe';
    let method = 'POST';
    
    // Handle different subscription actions
    if (action === 'assign_trainer') {
      endpoint = '/subscriptions/assign-trainer';
      method = 'POST';
    }
    
    // Forward request to Subscription Service
    const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const responseData = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { message: responseData.detail || 'Subscription operation failed' },
        { status: response.status }
      );
    }

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Subscription operation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
