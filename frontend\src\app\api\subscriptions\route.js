import { NextResponse } from 'next/server';

const SUBSCRIPTION_SERVICE_URL = process.env.SUBSCRIPTION_SERVICE_URL || 'http://localhost:8003';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');
    const trainerId = searchParams.get('trainer_id');
    const pending = searchParams.get('pending') === 'true';

    let endpoint = '/subscriptions/';

    if (pending) {
      endpoint = '/subscriptions/pending';
    } else if (userId) {
      endpoint = `/subscriptions/user/${userId}`;
    } else if (trainerId) {
      endpoint = `/subscriptions/trainer/${trainerId}`;
    }

    try {
      // Forward request to Subscription Service
      const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || 'Failed to fetch subscriptions');
      }

      return NextResponse.json(data);
    } catch (fetchError) {
      console.log('Subscription service not available, returning mock data');

      // Return mock data when service is not available
      if (pending) {
        const mockPendingSubscriptions = [
          {
            id: 1,
            user_id: 101,
            plan_id: 'premium',
            payment_amount: 59.99,
            status: 'pending',
            subscribed_at: new Date().toISOString()
          },
          {
            id: 2,
            user_id: 102,
            plan_id: 'basic',
            payment_amount: 29.99,
            status: 'pending',
            subscribed_at: new Date().toISOString()
          }
        ];
        return NextResponse.json(mockPendingSubscriptions);
      } else if (userId) {
        const mockUserSubscription = [
          {
            id: 1,
            user_id: parseInt(userId),
            plan_id: 'premium',
            payment_amount: 59.99,
            status: 'active',
            trainer_id: 1,
            subscribed_at: new Date().toISOString()
          }
        ];
        return NextResponse.json(mockUserSubscription);
      } else if (trainerId) {
        const mockTrainerClients = [
          {
            id: 1,
            user_id: 101,
            plan_id: 'premium',
            payment_amount: 59.99,
            status: 'active',
            trainer_id: parseInt(trainerId),
            subscribed_at: new Date().toISOString()
          }
        ];
        return NextResponse.json(mockTrainerClients);
      }

      return NextResponse.json([]);
    }

  } catch (error) {
    console.error('Subscriptions fetch error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action, ...data } = body;

    let endpoint = '/subscriptions/subscribe';
    let method = 'POST';

    // Handle different subscription actions
    if (action === 'assign_trainer') {
      endpoint = '/subscriptions/assign-trainer';
      method = 'POST';
    }

    try {
      // Forward request to Subscription Service
      const response = await fetch(`${SUBSCRIPTION_SERVICE_URL}${endpoint}`, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.detail || 'Subscription operation failed');
      }

      return NextResponse.json(responseData);
    } catch (fetchError) {
      console.log('Subscription service not available, returning mock response');

      // Return mock success response when service is not available
      if (action === 'assign_trainer') {
        return NextResponse.json({
          message: 'Trainer assigned successfully (mock)',
          subscription_id: data.subscription_id,
          trainer_id: data.trainer_id
        });
      }

      return NextResponse.json({
        message: 'Subscription operation completed (mock)',
        ...data
      });
    }

  } catch (error) {
    console.error('Subscription operation error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
