from sqlalchemy.orm import Session
from typing import Optional, List
import json
from ..models.plan import GymPlan
from ..schemas.plan import PlanCreate, PlanUpdate

class PlanService:
    """Service class for gym plan operations."""
    
    @staticmethod
    def get_plan(db: Session, plan_id: int) -> Optional[GymPlan]:
        """Get plan by ID."""
        return db.query(GymPlan).filter(GymPlan.id == plan_id).first()
    
    @staticmethod
    def get_plans(db: Session, skip: int = 0, limit: int = 100, active_only: bool = False) -> List[GymPlan]:
        """Get list of plans with pagination."""
        query = db.query(GymPlan)
        if active_only:
            query = query.filter(GymPlan.is_active == True)
        return query.offset(skip).limit(limit).all()
    
    @staticmethod
    def get_public_plans(db: Session) -> List[GymPlan]:
        """Get active plans for public display."""
        return db.query(GymPlan).filter(GymPlan.is_active == True).all()
    
    @staticmethod
    def create_plan(db: Session, plan: PlanCreate, created_by: int) -> GymPlan:
        """Create a new gym plan."""
        # Convert features list to JSON string
        features_json = json.dumps(plan.features) if plan.features else json.dumps([])
        
        db_plan = GymPlan(
            name=plan.name,
            description=plan.description,
            price=plan.price,
            duration_months=plan.duration_months,
            features=features_json,
            is_active=plan.is_active,
            created_by=created_by
        )
        db.add(db_plan)
        db.commit()
        db.refresh(db_plan)
        return db_plan
    
    @staticmethod
    def update_plan(db: Session, plan_id: int, plan_update: PlanUpdate) -> Optional[GymPlan]:
        """Update plan information."""
        db_plan = db.query(GymPlan).filter(GymPlan.id == plan_id).first()
        if not db_plan:
            return None
        
        update_data = plan_update.dict(exclude_unset=True)
        
        # Handle features update
        if "features" in update_data:
            update_data["features"] = json.dumps(update_data["features"])
        
        for field, value in update_data.items():
            setattr(db_plan, field, value)
        
        db.commit()
        db.refresh(db_plan)
        return db_plan
    
    @staticmethod
    def delete_plan(db: Session, plan_id: int) -> bool:
        """Delete plan by ID (soft delete by setting is_active to False)."""
        db_plan = db.query(GymPlan).filter(GymPlan.id == plan_id).first()
        if not db_plan:
            return False
        
        db_plan.is_active = False
        db.commit()
        return True
    
    @staticmethod
    def hard_delete_plan(db: Session, plan_id: int) -> bool:
        """Permanently delete plan by ID."""
        db_plan = db.query(GymPlan).filter(GymPlan.id == plan_id).first()
        if not db_plan:
            return False
        
        db.delete(db_plan)
        db.commit()
        return True
    
    @staticmethod
    def parse_features(plan: GymPlan) -> List[str]:
        """Parse features JSON string to list."""
        if not plan.features:
            return []
        try:
            return json.loads(plan.features)
        except json.JSONDecodeError:
            return []
    
    @staticmethod
    def get_plan_with_features(db: Session, plan_id: int) -> Optional[dict]:
        """Get plan with parsed features."""
        plan = PlanService.get_plan(db, plan_id)
        if not plan:
            return None
        
        return {
            "id": plan.id,
            "name": plan.name,
            "description": plan.description,
            "price": plan.price,
            "duration_months": plan.duration_months,
            "features": PlanService.parse_features(plan),
            "is_active": plan.is_active,
            "created_at": plan.created_at,
            "updated_at": plan.updated_at
        }
