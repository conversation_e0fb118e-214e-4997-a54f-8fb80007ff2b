var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/plans/route.js")
R.c("server/chunks/5991a_next_ef7897ae._.js")
R.c("server/chunks/[root-of-the-server]__d9a82f01._.js")
R.m("[project]/Documents/VS CODE/gym/frontend/.next-internal/server/app/api/plans/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Documents/VS CODE/gym/frontend/src/app/api/plans/route.js [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/Documents/VS CODE/gym/frontend/src/app/api/plans/route.js [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
