import { NextResponse } from 'next/server';

const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL || 'http://localhost:8000';

export async function GET(request) {
  try {
    // Forward request to Auth Service to get trainers
    const response = await fetch(`${AUTH_SERVICE_URL}/users?role=trainer`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      // If the auth service is not available, return mock data
      const mockTrainers = [
        {
          id: 1,
          specialization: "Weight Loss & Cardio",
          current_clients: 8,
          max_clients: 15,
          rating: 4.9
        },
        {
          id: 2,
          specialization: "Strength Training",
          current_clients: 12,
          max_clients: 15,
          rating: 4.8
        },
        {
          id: 3,
          specialization: "Yoga & Flexibility",
          current_clients: 6,
          max_clients: 12,
          rating: 4.7
        },
        {
          id: 4,
          specialization: "CrossFit & HIIT",
          current_clients: 15,
          max_clients: 15,
          rating: 4.9
        }
      ];
      
      return NextResponse.json(mockTrainers);
    }

    const data = await response.json();
    
    // Transform user data to trainer format
    const trainers = data.map(user => ({
      id: user.id,
      specialization: user.full_name || user.username,
      current_clients: Math.floor(Math.random() * 15),
      max_clients: 15,
      rating: (4.5 + Math.random() * 0.5).toFixed(1)
    }));

    return NextResponse.json(trainers);

  } catch (error) {
    console.error('Trainers fetch error:', error);
    
    // Return mock data as fallback
    const mockTrainers = [
      {
        id: 1,
        specialization: "Weight Loss & Cardio",
        current_clients: 8,
        max_clients: 15,
        rating: 4.9
      },
      {
        id: 2,
        specialization: "Strength Training",
        current_clients: 12,
        max_clients: 15,
        rating: 4.8
      },
      {
        id: 3,
        specialization: "Yoga & Flexibility",
        current_clients: 6,
        max_clients: 12,
        rating: 4.7
      },
      {
        id: 4,
        specialization: "CrossFit & HIIT",
        current_clients: 15,
        max_clients: 15,
        rating: 4.9
      }
    ];
    
    return NextResponse.json(mockTrainers);
  }
}
