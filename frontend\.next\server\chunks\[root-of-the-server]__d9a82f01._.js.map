{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/api/plans/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nconst PLAN_SERVICE_URL = process.env.PLAN_SERVICE_URL || 'http://localhost:8002';\n\nexport async function GET(request) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const publicOnly = searchParams.get('public') === 'true';\n    \n    // Determine endpoint based on request type\n    const endpoint = publicOnly ? '/plans/public' : '/plans/';\n    \n    // Forward request to Plan Service\n    const response = await fetch(`${PLAN_SERVICE_URL}${endpoint}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return NextResponse.json(\n        { message: data.detail || 'Failed to fetch plans' },\n        { status: response.status }\n      );\n    }\n\n    return NextResponse.json(data);\n\n  } catch (error) {\n    console.error('Plans fetch error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request) {\n  try {\n    const authHeader = request.headers.get('authorization');\n    const body = await request.json();\n    \n    if (!authHeader) {\n      return NextResponse.json(\n        { message: 'Authorization header required' },\n        { status: 401 }\n      );\n    }\n\n    // Forward request to Plan Service\n    const response = await fetch(`${PLAN_SERVICE_URL}/plans/`, {\n      method: 'POST',\n      headers: {\n        'Authorization': authHeader,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(body),\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      return NextResponse.json(\n        { message: data.detail || 'Failed to create plan' },\n        { status: response.status }\n      );\n    }\n\n    return NextResponse.json(data);\n\n  } catch (error) {\n    console.error('Plan creation error:', error);\n    return NextResponse.json(\n      { message: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB,IAAI;AAElD,eAAe,IAAI,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC,cAAc;QAElD,2CAA2C;QAC3C,MAAM,WAAW,aAAa,kBAAkB;QAEhD,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,GAAG,mBAAmB,UAAU,EAAE;YAC7D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,KAAK,MAAM,IAAI;YAAwB,GAClD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,IAAI,CAAC,YAAY;YACf,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;YAAgC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,GAAG,iBAAiB,OAAO,CAAC,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,iBAAiB;gBACjB,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO,4LAAY,CAAC,IAAI,CACtB;gBAAE,SAAS,KAAK,MAAM,IAAI;YAAwB,GAClD;gBAAE,QAAQ,SAAS,MAAM;YAAC;QAE9B;QAEA,OAAO,4LAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,4LAAY,CAAC,IAAI,CACtB;YAAE,SAAS;QAAwB,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}