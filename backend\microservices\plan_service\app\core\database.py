from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings
import os

# Get absolute path to database
db_path = os.path.abspath(os.path.join(os.path.dirname(__file__), settings.database_url.replace("sqlite:///", "")))
database_url = f"sqlite:///{db_path}"

# Create SQLAlchemy engine
engine = create_engine(
    database_url,
    connect_args={"check_same_thread": False}
)

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for models
Base = declarative_base()

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
