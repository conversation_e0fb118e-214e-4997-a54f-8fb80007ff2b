#!/usr/bin/env python3
"""
Database initialization script for FitLife Gym.
Creates the database and populates it with seed data.
"""

import sqlite3
import hashlib
import json
from datetime import datetime, timedelta
import os

def hash_password(password):
    """Return plain text password for development."""
    return password

def create_database():
    """Create the database and tables."""
    db_path = os.path.join(os.path.dirname(__file__), 'gym_app.db')
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # Create new database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Read and execute schema
    schema_path = os.path.join(os.path.dirname(__file__), 'schema.sql')
    with open(schema_path, 'r') as f:
        schema = f.read()
    
    cursor.executescript(schema)
    conn.commit()
    
    return conn

def insert_seed_data(conn):
    """Insert seed data into the database."""
    cursor = conn.cursor()
    
    # Insert Users
    users_data = [
        # Admin user
        ('admin', '<EMAIL>', 'admin', 'admin', 'Admin User', '******-0001', '123 Admin St', '1985-01-01', 'other', 'Emergency Admin', '******-0002'),

        # Trainer users
        ('sarah_johnson', '<EMAIL>', 'trainer', 'trainer', '<PERSON> <PERSON>', '******-0101', '456 Trainer Ave', '1990-03-15', 'female', 'John Johnson', '******-0102'),
        ('mike_rodriguez', '<EMAIL>', 'trainer', 'trainer', 'Mike Rodriguez', '******-0201', '789 Fitness Blvd', '1988-07-22', 'male', 'Maria Rodriguez', '******-0202'),
        ('emily_chen', '<EMAIL>', 'trainer', 'trainer', 'Emily Chen', '******-0301', '321 Wellness Way', '1992-11-08', 'female', 'David Chen', '******-0302'),
        ('david_thompson', '<EMAIL>', 'trainer', 'trainer', 'David Thompson', '******-0401', '654 Strong St', '1987-05-30', 'male', 'Lisa Thompson', '******-0402'),

        # Customer users
        ('customer', '<EMAIL>', 'customer', 'customer', 'John Customer', '******-1001', '111 Customer Lane', '1995-09-12', 'male', 'Jane Customer', '******-1002'),
        ('jessica_martinez', '<EMAIL>', 'password', 'customer', 'Jessica Martinez', '******-1101', '222 Fitness Road', '1993-04-18', 'female', 'Carlos Martinez', '******-1102'),
        ('robert_chen', '<EMAIL>', 'password', 'customer', 'Robert Chen', '******-1201', '333 Health Ave', '1991-12-03', 'male', 'Linda Chen', '******-1202'),
        ('amanda_foster', '<EMAIL>', 'password', 'customer', 'Amanda Foster', '******-1301', '444 Wellness Dr', '1994-08-25', 'female', 'Mark Foster', '******-1302'),
        ('david_wilson', '<EMAIL>', 'password', 'customer', 'David Wilson', '******-1401', '555 Strength St', '1989-06-14', 'male', 'Sarah Wilson', '******-1402'),
    ]
    
    cursor.executemany('''
        INSERT INTO users (username, email, password_hash, role, full_name, phone, address, date_of_birth, gender, emergency_contact, emergency_phone)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', users_data)
    
    # Insert Gym Plans
    plans_data = [
        ('Basic Fitness', 'Perfect for beginners starting their fitness journey', 29.99, 1, json.dumps([
            "Access to gym equipment",
            "Basic workout plans",
            "Monthly progress tracking",
            "Email support"
        ])),
        ('Premium Training', 'Most popular choice for serious fitness enthusiasts', 59.99, 1, json.dumps([
            "Everything in Basic",
            "Personal trainer assignment",
            "Custom diet plans",
            "Weekly progress reviews",
            "Priority support"
        ])),
        ('Elite Transformation', 'Complete transformation package with premium support', 99.99, 1, json.dumps([
            "Everything in Premium",
            "1-on-1 training sessions",
            "Nutrition consultation",
            "Daily meal planning",
            "24/7 trainer support",
            "Body composition analysis"
        ])),
    ]
    
    cursor.executemany('''
        INSERT INTO gym_plans (name, description, price, duration_months, features, created_by)
        VALUES (?, ?, ?, ?, ?, 1)
    ''', plans_data)
    
    # Insert Trainers
    trainers_data = [
        (2, 'Weight Loss & Cardio', 8, 'Specializes in helping clients achieve sustainable weight loss through personalized cardio and strength training programs.', json.dumps(['NASM-CPT', 'Nutrition Specialist']), 75.00, 20, 5, 4.9, 45),
        (3, 'Strength Training & Bodybuilding', 12, 'Expert in muscle building and strength development with a focus on proper form and progressive overload.', json.dumps(['ACSM-CPT', 'Powerlifting Coach']), 85.00, 15, 8, 4.8, 62),
        (4, 'Yoga & Flexibility', 6, 'Combines traditional yoga practices with modern fitness techniques for improved flexibility and mindfulness.', json.dumps(['RYT-500', 'Pilates Instructor']), 65.00, 25, 12, 4.9, 38),
        (5, 'HIIT & Functional Training', 10, 'High-intensity interval training expert focused on functional movements and athletic performance.', json.dumps(['CrossFit Level 2', 'TRX Certified']), 80.00, 18, 6, 4.7, 51),
    ]
    
    cursor.executemany('''
        INSERT INTO trainers (user_id, specialization, experience_years, bio, certifications, hourly_rate, max_clients, current_clients, rating, total_reviews)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', trainers_data)
    
    # Insert Sample Subscriptions
    subscriptions_data = [
        (6, 2, 2, 'active', datetime.now() - timedelta(days=15), datetime.now() + timedelta(days=15), datetime.now() - timedelta(days=10), 'paid', 59.99),
        (7, 1, 2, 'active', datetime.now() - timedelta(days=5), datetime.now() + timedelta(days=25), datetime.now() - timedelta(days=3), 'paid', 29.99),
        (8, 3, 3, 'active', datetime.now() - timedelta(days=20), datetime.now() + timedelta(days=10), datetime.now() - timedelta(days=18), 'paid', 99.99),
        (9, 2, 4, 'pending', datetime.now() - timedelta(days=1), datetime.now() + timedelta(days=29), None, 'pending', 59.99),
        (10, 1, None, 'pending', datetime.now(), datetime.now() + timedelta(days=30), None, 'pending', 29.99),
    ]
    
    cursor.executemany('''
        INSERT INTO subscriptions (user_id, plan_id, trainer_id, status, subscribed_at, expires_at, assigned_at, payment_status, payment_amount)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', subscriptions_data)
    
    # Insert Sample Calendar Events
    events_data = [
        (6, 2, 'Initial Consultation', 'First meeting to discuss goals and create plan', datetime.now() + timedelta(days=1), '10:00', '11:00', 'consultation', 'scheduled', 'Gym Studio A'),
        (7, 2, 'Workout Session', 'Cardio and strength training session', datetime.now() + timedelta(days=2), '14:00', '15:00', 'workout', 'scheduled', 'Main Gym Floor'),
        (8, 3, 'Nutrition Consultation', 'Review diet plan and meal prep', datetime.now() + timedelta(days=3), '09:00', '10:00', 'consultation', 'scheduled', 'Nutrition Office'),
        (6, 2, 'Progress Check', 'Weekly progress review and plan adjustment', datetime.now() + timedelta(days=7), '16:00', '17:00', 'assessment', 'scheduled', 'Trainer Office'),
    ]
    
    cursor.executemany('''
        INSERT INTO calendar_events (user_id, trainer_id, title, description, event_date, start_time, end_time, event_type, status, location)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', events_data)
    
    conn.commit()
    print("✅ Seed data inserted successfully!")

def main():
    """Main function to initialize the database."""
    print("🏋️ Initializing FitLife Gym Database...")
    
    # Create database and tables
    conn = create_database()
    print("✅ Database and tables created successfully!")
    
    # Insert seed data
    insert_seed_data(conn)
    
    # Close connection
    conn.close()
    
    print("\n🎉 Database initialization completed!")
    print("\n📊 Demo Data Summary:")
    print("👤 Users: 1 Admin, 4 Trainers, 5 Customers")
    print("💪 Plans: 3 Gym Plans (Basic, Premium, Elite)")
    print("📅 Subscriptions: 5 Sample subscriptions")
    print("🗓️ Events: 4 Sample calendar events")
    print("\n🔑 Demo Credentials:")
    print("Admin:    username=admin,    password=admin")
    print("Trainer:  username=sarah_johnson, password=trainer")
    print("Customer: username=customer, password=customer")

if __name__ == "__main__":
    main()
