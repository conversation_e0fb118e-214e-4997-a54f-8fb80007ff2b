var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/pages/_error.js")
R.c("server/chunks/ssr/5991a_51966409._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e6a4d965._.js")
R.c("server/chunks/ssr/5991a_next_cf5b4ce1._.js")
R.c("server/chunks/ssr/5991a_ffbe9e7c._.js")
R.c("server/chunks/ssr/[externals]_next_dist_shared_lib_no-fallback-error_external_59b92b38.js")
R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)")
module.exports=R.m("[project]/Documents/VS CODE/gym/frontend/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/Documents/VS CODE/gym/frontend/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)").exports
