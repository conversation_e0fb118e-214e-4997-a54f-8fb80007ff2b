'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useAuth } from '@/components/auth/AuthProvider';

const PlanGrid = () => {
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);

  useEffect(() => {
    // Fetch plans from API
    const fetchPlans = async () => {
      try {
        const response = await fetch('/api/plans?public=true');
        if (response.ok) {
          const data = await response.json();
          // Mark the second plan as popular (or use backend data)
          const plansWithPopular = data.map((plan, index) => ({
            ...plan,
            popular: index === 1, // Make second plan popular
            duration: plan.duration_months
          }));
          setPlans(plansWithPopular);
        } else {
          console.error('Failed to fetch plans');
          // Fallback to sample data
          setPlans(getSamplePlans());
        }
      } catch (error) {
        console.error('Error fetching plans:', error);
        // Fallback to sample data
        setPlans(getSamplePlans());
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  const getSamplePlans = () => [
    {
      id: 1,
      name: "Basic Fitness",
      price: 29.99,
      duration_months: 1,
      popular: false,
      features: [
        "Access to gym equipment",
        "Basic workout plans",
        "Monthly progress tracking",
        "Email support"
      ],
      description: "Perfect for beginners starting their fitness journey"
    },
    {
      id: 2,
      name: "Premium Training",
      price: 59.99,
      duration_months: 1,
      popular: true,
      features: [
        "Everything in Basic",
        "Personal trainer assignment",
        "Custom diet plans",
        "Weekly progress reviews",
        "Priority support"
      ],
      description: "Most popular choice for serious fitness enthusiasts"
    },
    {
      id: 3,
      name: "Elite Transformation",
      price: 99.99,
      duration_months: 1,
      popular: false,
      features: [
        "Everything in Premium",
        "1-on-1 training sessions",
        "Nutrition consultation",
        "Daily meal planning",
        "24/7 trainer support",
        "Body composition analysis"
      ],
      description: "Complete transformation package with premium support"
    }
  ];

  const handleSubscribe = (plan) => {
    if (!isAuthenticated) {
      // Redirect to login if not authenticated
      router.push('/login');
      return;
    }

    // Show subscription modal
    setSelectedPlan(plan);
    setShowSubscriptionModal(true);
  };

  const processSubscription = async () => {
    if (!selectedPlan || !user) return;

    try {
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          plan_id: selectedPlan.id,
          payment_amount: selectedPlan.price,
        }),
      });

      if (response.ok) {
        alert(`Successfully subscribed to ${selectedPlan.name}! You will be assigned a trainer soon.`);
        setShowSubscriptionModal(false);
        setSelectedPlan(null);

        // Redirect to customer dashboard
        router.push('/customer');
      } else {
        alert('Subscription failed. Please try again.');
      }
    } catch (error) {
      console.error('Subscription error:', error);
      alert('Network error. Please try again.');
    }
  };

  const closeModal = () => {
    setShowSubscriptionModal(false);
    setSelectedPlan(null);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2 mb-6">
              {[1, 2, 3, 4].map((j) => (
                <div key={j} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {plans.map((plan) => (
        <div
          key={plan.id}
          className={`relative bg-white rounded-lg shadow-lg overflow-hidden transform transition-transform hover:scale-105 ${
            plan.popular ? 'ring-2 ring-yellow-400' : ''
          }`}
        >
          {/* Popular Badge */}
          {plan.popular && (
            <div className="absolute top-0 right-0 bg-yellow-400 text-black px-3 py-1 text-sm font-bold">
              Most Popular
            </div>
          )}
          
          <div className="p-6">
            {/* Plan Name */}
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
            
            {/* Price */}
            <div className="mb-4">
              <span className="text-4xl font-extrabold text-gray-900">${plan.price}</span>
              <span className="text-gray-600">/month</span>
            </div>
            
            {/* Description */}
            <p className="text-gray-600 mb-6">{plan.description}</p>
            
            {/* Features */}
            <ul className="space-y-3 mb-8">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center">
                  <svg
                    className="w-5 h-5 text-green-500 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>
            
            {/* Subscribe Button */}
            <Button
              fullWidth
              size="large"
              variant={plan.popular ? 'primary' : 'outline'}
              onClick={() => handleSubscribe(plan)}
              className={plan.popular ? 'bg-yellow-400 hover:bg-yellow-500 text-black' : ''}
            >
              Choose Plan
            </Button>
          </div>
        </div>
      ))}

      {/* Subscription Modal */}
      {showSubscriptionModal && selectedPlan && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Confirm Subscription</h2>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{selectedPlan.name}</h3>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  ${selectedPlan.price}<span className="text-lg text-gray-600">/month</span>
                </div>
                <p className="text-gray-600">{selectedPlan.description}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-2">What's included:</h4>
                <ul className="space-y-1">
                  {selectedPlan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="text-sm text-gray-500 mb-6">
                <p>• You will be assigned a qualified trainer within 24 hours</p>
                <p>• Your subscription will auto-renew monthly</p>
                <p>• Cancel anytime from your dashboard</p>
              </div>
            </div>
            <div className="p-6 border-t border-gray-200 flex space-x-3">
              <Button onClick={closeModal} variant="outline" className="flex-1">
                Cancel
              </Button>
              <Button onClick={processSubscription} className="flex-1">
                Subscribe Now
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlanGrid;
