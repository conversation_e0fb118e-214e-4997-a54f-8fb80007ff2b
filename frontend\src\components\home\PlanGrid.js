'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';

const PlanGrid = () => {
  const router = useRouter();
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch plans from API
    const fetchPlans = async () => {
      try {
        const response = await fetch('/api/plans?public=true');
        if (response.ok) {
          const data = await response.json();
          // Mark the second plan as popular (or use backend data)
          const plansWithPopular = data.map((plan, index) => ({
            ...plan,
            popular: index === 1, // Make second plan popular
            duration: plan.duration_months
          }));
          setPlans(plansWithPopular);
        } else {
          console.error('Failed to fetch plans');
          // Fallback to sample data
          setPlans(getSamplePlans());
        }
      } catch (error) {
        console.error('Error fetching plans:', error);
        // Fallback to sample data
        setPlans(getSamplePlans());
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, []);

  const getSamplePlans = () => [
    {
      id: 1,
      name: "Basic Fitness",
      price: 29.99,
      duration_months: 1,
      popular: false,
      features: [
        "Access to gym equipment",
        "Basic workout plans",
        "Monthly progress tracking",
        "Email support"
      ],
      description: "Perfect for beginners starting their fitness journey"
    },
    {
      id: 2,
      name: "Premium Training",
      price: 59.99,
      duration_months: 1,
      popular: true,
      features: [
        "Everything in Basic",
        "Personal trainer assignment",
        "Custom diet plans",
        "Weekly progress reviews",
        "Priority support"
      ],
      description: "Most popular choice for serious fitness enthusiasts"
    },
    {
      id: 3,
      name: "Elite Transformation",
      price: 99.99,
      duration_months: 1,
      popular: false,
      features: [
        "Everything in Premium",
        "1-on-1 training sessions",
        "Nutrition consultation",
        "Daily meal planning",
        "24/7 trainer support",
        "Body composition analysis"
      ],
      description: "Complete transformation package with premium support"
    }
  ];

  const handleSubscribe = (planId) => {
    // Check if user is logged in
    const token = localStorage.getItem('accessToken');
    if (!token) {
      router.push('/login');
      return;
    }
    
    // Redirect to subscription flow
    router.push(`/subscribe/${planId}`);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow-lg p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-2 mb-6">
              {[1, 2, 3, 4].map((j) => (
                <div key={j} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {plans.map((plan) => (
        <div
          key={plan.id}
          className={`relative bg-white rounded-lg shadow-lg overflow-hidden transform transition-transform hover:scale-105 ${
            plan.popular ? 'ring-2 ring-yellow-400' : ''
          }`}
        >
          {/* Popular Badge */}
          {plan.popular && (
            <div className="absolute top-0 right-0 bg-yellow-400 text-black px-3 py-1 text-sm font-bold">
              Most Popular
            </div>
          )}
          
          <div className="p-6">
            {/* Plan Name */}
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
            
            {/* Price */}
            <div className="mb-4">
              <span className="text-4xl font-extrabold text-gray-900">${plan.price}</span>
              <span className="text-gray-600">/month</span>
            </div>
            
            {/* Description */}
            <p className="text-gray-600 mb-6">{plan.description}</p>
            
            {/* Features */}
            <ul className="space-y-3 mb-8">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-center">
                  <svg
                    className="w-5 h-5 text-green-500 mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>
            
            {/* Subscribe Button */}
            <Button
              fullWidth
              size="large"
              variant={plan.popular ? 'primary' : 'outline'}
              onClick={() => handleSubscribe(plan.id)}
              className={plan.popular ? 'bg-yellow-400 hover:bg-yellow-500 text-black' : ''}
            >
              Choose Plan
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PlanGrid;
