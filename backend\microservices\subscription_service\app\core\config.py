from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # Application settings
    app_name: str = "FitLife Gym - Subscription Service"
    debug: bool = False
    version: str = "1.0.0"
    
    # Database settings
    database_url: str = "sqlite:///../../database/gym_app.db"
    
    # CORS settings
    allowed_origins: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
    ]
    
    # API settings
    api_v1_prefix: str = "/api/v1"
    
    # WebSocket settings for real-time notifications
    websocket_url: str = "ws://localhost:8003/ws"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Create settings instance
settings = Settings()
