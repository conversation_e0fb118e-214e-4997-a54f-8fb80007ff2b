module.exports = [
"[project]/Documents/VS CODE/gym/frontend/.next-internal/server/app/customer/page/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/VS CODE/gym/frontend/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/Documents/VS CODE/gym/frontend/src/app/layout.js [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/VS CODE/gym/frontend/src/app/layout.js [app-rsc] (ecmascript)"));
}),
"[project]/Documents/VS CODE/gym/frontend/src/app/customer/page.js [app-rsc] (ecmascript)", ((__turbopack_context__, module, exports) => {

const e = new Error("Could not parse module '[project]/Documents/VS CODE/gym/frontend/src/app/customer/page.js'\n\nUnterminated regexp literal");
e.code = 'MODULE_UNPARSABLE';
throw e;
}),
"[project]/Documents/VS CODE/gym/frontend/src/app/customer/page.js [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/Documents/VS CODE/gym/frontend/src/app/customer/page.js [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__5652db92._.js.map