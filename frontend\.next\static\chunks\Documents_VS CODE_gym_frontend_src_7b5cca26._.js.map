{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Button.js"], "sourcesContent": ["import React from 'react';\n\nconst Button = ({ \n  children, \n  variant = 'primary', \n  size = 'medium', \n  disabled = false, \n  loading = false, \n  fullWidth = false,\n  onClick,\n  type = 'button',\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',\n    success: 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500',\n    danger: 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500',\n    warning: 'bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500',\n    outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',\n    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500',\n    admin: 'bg-purple-600 hover:bg-purple-700 text-white focus:ring-purple-500',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-1 text-xs',\n    small: 'px-3 py-1.5 text-sm',\n    medium: 'px-4 py-2 text-sm',\n    large: 'px-6 py-3 text-base',\n    'extra-large': 'px-8 py-4 text-lg',\n  };\n\n  const widthClass = fullWidth ? 'w-full' : '';\n  \n  const buttonClasses = `\n    ${baseClasses}\n    ${variants[variant] || variants.primary}\n    ${sizes[size] || sizes.medium}\n    ${widthClass}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <svg \n          className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-current\" \n          fill=\"none\" \n          viewBox=\"0 0 24 24\"\n        >\n          <circle \n            className=\"opacity-25\" \n            cx=\"12\" \n            cy=\"12\" \n            r=\"10\" \n            stroke=\"currentColor\" \n            strokeWidth=\"4\"\n          />\n          <path \n            className=\"opacity-75\" \n            fill=\"currentColor\" \n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          />\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\n// Flight Booking Buttons\nexport const SearchFlightButton = (props) => (\n  <Button variant=\"primary\" {...props}>Search Flight</Button>\n);\n\nexport const BookNowButton = (props) => (\n  <Button variant=\"primary\" {...props}>Book Now</Button>\n);\n\nexport const CancelButton = (props) => (\n  <Button variant=\"secondary\" {...props}>Cancel</Button>\n);\n\nexport const ViewDetailsButton = (props) => (\n  <Button variant=\"outline\" {...props}>View Details</Button>\n);\n\nexport const SkipButton = (props) => (\n  <Button variant=\"ghost\" {...props}>Skip</Button>\n);\n\n// Admin Buttons\nexport const SaveChangesButton = (props) => (\n  <Button variant=\"admin\" {...props}>Save Changes</Button>\n);\n\nexport const EditButton = (props) => (\n  <Button variant=\"outline\" {...props}>Edit</Button>\n);\n\nexport const AddOptionsButton = (props) => (\n  <Button variant=\"admin\" {...props}>+ Add Options</Button>\n);\n\n// Status Buttons\nexport const ConfirmButton = (props) => (\n  <Button variant=\"success\" {...props}>Confirm</Button>\n);\n\nexport const DeleteButton = (props) => (\n  <Button variant=\"danger\" {...props}>Delete</Button>\n);\n\nexport const PendingButton = (props) => (\n  <Button variant=\"warning\" {...props}>Pending</Button>\n);\n\n// Loading Button\nexport const LoadingButton = (props) => (\n  <Button loading {...props}>Loading...</Button>\n);\n\n// Disabled Button\nexport const DisabledButton = (props) => (\n  <Button disabled variant=\"secondary\" {...props}>Disabled</Button>\n);\n\nexport default Button;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,SAAS;QAAC,EACd,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,YAAY,KAAK,EACjB,OAAO,EACP,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,OAAO;QACP,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,aAAa,YAAY,WAAW;IAE1C,MAAM,gBAAgB,AAAC,SAEnB,OADA,aAAY,UAEZ,OADA,QAAQ,CAAC,QAAQ,IAAI,SAAS,OAAO,EAAC,UAEtC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,MAAM,EAAC,UAE5B,OADA,YAAW,UACD,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QACC,MAAM;QACN,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,yOAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;;kCAER,yOAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,yOAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;KA3EM;AA8EC,MAAM,qBAAqB,CAAC,sBACjC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD5B;AAIN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,oBAAoB,CAAC,sBAChC,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,mBAAmB,CAAC,sBAC/B,yOAAC;QAAO,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADxB;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAO,SAAQ;QAAU,GAAG,KAAK;kBAAE;;;;;;OADzB;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD1B;AAKN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAO,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADhB;AAKN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAO,QAAQ;QAAC,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OADrC;uCAIE", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Navbar.js"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Button from '../ui/Button';\nimport { useAuth } from '../auth/AuthProvider';\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const { isAuthenticated, user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    await logout();\n    router.push('/');\n  };\n\n  const getDashboardLink = () => {\n    if (!user) return '/';\n\n    switch (user.role) {\n      case 'admin':\n        return '/admin';\n      case 'trainer':\n        return '/trainer';\n      case 'customer':\n        return '/customer';\n      default:\n        return '/';\n    }\n  };\n\n  const navLinks = [\n    { href: '#plans', label: 'Plans' },\n    { href: '#trainers', label: 'Trainers' },\n    { href: '#testimonials', label: 'Success Stories' },\n    { href: '#contact', label: 'Contact' },\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0 flex items-center cursor-pointer\" onClick={() => router.push('/')}>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-700 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-lg\">F</span>\n                </div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  FitLife Gym\n                </h1>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:ml-8 md:flex md:space-x-8\">\n              {navLinks.map((link) => (\n                <a\n                  key={link.href}\n                  href={link.href}\n                  className=\"text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n                >\n                  {link.label}\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {isAuthenticated && user ? (\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"hidden md:flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome,</span>\n                  <span className=\"text-sm font-medium text-gray-900\">\n                    {user.username}\n                  </span>\n                </div>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push(getDashboardLink())}\n                >\n                  Dashboard\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={handleLogout}\n                >\n                  Logout\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <Button\n                  variant=\"outline\"\n                  size=\"small\"\n                  onClick={() => router.push('/login')}\n                >\n                  Login\n                </Button>\n\n                <Button\n                  size=\"small\"\n                  className=\"bg-yellow-400 hover:bg-yellow-500 text-black\"\n                  onClick={() => router.push('/register')}\n                >\n                  Join Now\n                </Button>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 p-2 rounded-md\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {!isMenuOpen ? (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  </svg>\n                ) : (\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 border-t border-gray-200\">\n            {/* Mobile Navigation Links */}\n            {navLinks.map((link) => (\n              <a\n                key={link.href}\n                href={link.href}\n                className=\"text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {link.label}\n              </a>\n            ))}\n\n            {/* Mobile User Actions */}\n            <div className=\"border-t border-gray-200 pt-4 mt-4\">\n              {isAuthenticated && user ? (\n                <div className=\"space-y-2\">\n                  <div className=\"px-3 py-2\">\n                    <div className=\"text-sm font-medium text-gray-900\">\n                      Welcome, {user.username}\n                    </div>\n                    <div className=\"text-xs text-gray-600 capitalize\">\n                      {user.role}\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => {\n                      router.push(getDashboardLink());\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Dashboard\n                  </button>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-2\">\n                  <button\n                    onClick={() => {\n                      router.push('/login');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left text-gray-600 hover:text-gray-900 hover:bg-gray-100 block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Login\n                  </button>\n                  <button\n                    onClick={() => {\n                      router.push('/register');\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full text-left bg-yellow-400 hover:bg-yellow-500 text-black block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  >\n                    Join Now\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,qNAAQ,EAAC;IAC7C,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAA,mMAAO;IACjD,MAAM,SAAS,IAAA,8LAAS;IAExB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAa,OAAO;QAAW;QACvC;YAAE,MAAM;YAAiB,OAAO;QAAkB;QAClD;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE,yOAAC;QAAI,WAAU;;0BACb,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;oCAAiD,SAAS,IAAM,OAAO,IAAI,CAAC;8CACzF,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,yOAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;;;;;;;8CAOpD,yOAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,KAAK;2CAJN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAWtB,yOAAC;4BAAI,WAAU;;gCACZ,mBAAmB,qBAClB,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAK,WAAU;8DAAwB;;;;;;8DACxC,yOAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;;;;;;;sDAIlB,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDACV;;;;;;;;;;;6FAKH,yOAAC;oCAAI,WAAU;;sDACb,yOAAC,2LAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;sDAID,yOAAC,2LAAM;4CACL,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;sDAC5B;;;;;;;;;;;;8CAOL,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,yOAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,CAAC,2BACA,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;yGAGvE,yOAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,yOAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,4BACC,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;wBAEZ,SAAS,GAAG,CAAC,CAAC,qBACb,yOAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;sCAUlB,yOAAC;4BAAI,WAAU;sCACZ,mBAAmB,qBAClB,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;;oDAAoC;oDACvC,KAAK,QAAQ;;;;;;;0DAEzB,yOAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;;;;;;;kDAGd,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP;4CACA,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;yFAKH,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;kDAGD,yOAAC;wCACC,SAAS;4CACP,OAAO,IAAI,CAAC;4CACZ,cAAc;wCAChB;wCACA,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA/MM;;QAEsC,mMAAO;QAClC,8LAAS;;;KAHpB;uCAiNS", "debugId": null}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/common/Layout.js"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Navbar from './Navbar';\nimport { useAuth } from '../auth/AuthProvider';\n\nconst Layout = ({ children, showNavbar = true, className = '' }) => {\n  const { isAuthenticated } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {showNavbar && isAuthenticated && <Navbar />}\n      \n      <main className={`${className}`}>\n        {children}\n      </main>\n    </div>\n  );\n};\n\n// Dashboard Layout with sidebar\nexport const DashboardLayout = ({ \n  children, \n  title, \n  subtitle,\n  actions,\n  className = '' \n}) => {\n  return (\n    <Layout className=\"py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Page Header */}\n        <div className=\"mb-8\">\n          <div className=\"md:flex md:items-center md:justify-between\">\n            <div className=\"flex-1 min-w-0\">\n              <h1 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n                {title}\n              </h1>\n              {subtitle && (\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  {subtitle}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"mt-4 flex md:mt-0 md:ml-4\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Page Content */}\n        <div className={className}>\n          {children}\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\n// Card Layout for content sections\nexport const CardLayout = ({ \n  children, \n  title, \n  subtitle,\n  actions,\n  className = '',\n  padding = true \n}) => {\n  return (\n    <div className={`bg-white shadow rounded-lg ${className}`}>\n      {(title || subtitle || actions) && (\n        <div className={`${padding ? 'px-4 py-5 sm:px-6' : 'px-6 py-4'} border-b border-gray-200`}>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              {title && (\n                <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n                  {title}\n                </h3>\n              )}\n              {subtitle && (\n                <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                  {subtitle}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex space-x-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n      \n      <div className={padding ? 'px-4 py-5 sm:p-6' : ''}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Stats Layout for dashboard metrics\nexport const StatsLayout = ({ stats = [] }) => {\n  return (\n    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n      {stats.map((stat, index) => (\n        <div key={index} className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                {stat.icon && (\n                  <div className={`w-8 h-8 ${stat.iconColor || 'text-gray-400'}`}>\n                    {stat.icon}\n                  </div>\n                )}\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                    {stat.label}\n                  </dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">\n                    {stat.value}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          {stat.change && (\n            <div className=\"bg-gray-50 px-5 py-3\">\n              <div className=\"text-sm\">\n                <span className={`font-medium ${\n                  stat.changeType === 'increase' ? 'text-green-600' : \n                  stat.changeType === 'decrease' ? 'text-red-600' : \n                  'text-gray-600'\n                }`}>\n                  {stat.change}\n                </span>\n                <span className=\"text-gray-500\"> from last month</span>\n              </div>\n            </div>\n          )}\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// Grid Layout for responsive content\nexport const GridLayout = ({ \n  children, \n  cols = 1, \n  gap = 6, \n  className = '' \n}) => {\n  const gridClasses = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\n  };\n\n  const gapClasses = {\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  return (\n    <div className={`grid ${gridClasses[cols]} ${gapClasses[gap]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;QAAC,EAAE,QAAQ,EAAE,aAAa,IAAI,EAAE,YAAY,EAAE,EAAE;;IAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,mMAAO;IAEnC,qBACE,yOAAC;QAAI,WAAU;;YACZ,cAAc,iCAAmB,yOAAC,+LAAM;;;;;0BAEzC,yOAAC;gBAAK,WAAW,AAAC,GAAY,OAAV;0BACjB;;;;;;;;;;;;AAIT;GAZM;;QACwB,mMAAO;;;KAD/B;AAeC,MAAM,kBAAkB;QAAC,EAC9B,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACf;IACC,qBACE,yOAAC;QAAO,WAAU;kBAChB,cAAA,yOAAC;YAAI,WAAU;;8BAEb,yOAAC;oBAAI,WAAU;8BACb,cAAA,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAG,WAAU;kDACX;;;;;;oCAEF,0BACC,yOAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;4BAIN,yBACC,yOAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;8BAOT,yOAAC;oBAAI,WAAW;8BACb;;;;;;;;;;;;;;;;;AAKX;MAtCa;AAyCN,MAAM,aAAa;QAAC,EACzB,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACd,UAAU,IAAI,EACf;IACC,qBACE,yOAAC;QAAI,WAAW,AAAC,8BAAuC,OAAV;;YAC3C,CAAC,SAAS,YAAY,OAAO,mBAC5B,yOAAC;gBAAI,WAAW,AAAC,GAA8C,OAA5C,UAAU,sBAAsB,aAAY;0BAC7D,cAAA,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;;gCACE,uBACC,yOAAC;oCAAG,WAAU;8CACX;;;;;;gCAGJ,0BACC,yOAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;wBAIN,yBACC,yOAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAOX,yOAAC;gBAAI,WAAW,UAAU,qBAAqB;0BAC5C;;;;;;;;;;;;AAIT;MAvCa;AA0CN,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAE,EAAE;IACxC,qBACE,yOAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,yOAAC;gBAAgB,WAAU;;kCACzB,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,kBACR,yOAAC;wCAAI,WAAW,AAAC,WAA4C,OAAlC,KAAK,SAAS,IAAI;kDAC1C,KAAK,IAAI;;;;;;;;;;;8CAIhB,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;;0DACC,yOAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,yOAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAMpB,KAAK,MAAM,kBACV,yOAAC;wBAAI,WAAU;kCACb,cAAA,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAK,WAAW,AAAC,eAIjB,OAHC,KAAK,UAAU,KAAK,aAAa,mBACjC,KAAK,UAAU,KAAK,aAAa,iBACjC;8CAEC,KAAK,MAAM;;;;;;8CAEd,yOAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;eAhC9B;;;;;;;;;;AAwClB;MA5Ca;AA+CN,MAAM,aAAa;QAAC,EACzB,QAAQ,EACR,OAAO,CAAC,EACR,MAAM,CAAC,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,aAAa;QACjB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,yOAAC;QAAI,WAAW,AAAC,QAA4B,OAArB,WAAW,CAAC,KAAK,EAAC,KAAsB,OAAnB,UAAU,CAAC,IAAI,EAAC,KAAa,OAAV;kBAC7D;;;;;;AAGP;MAxBa;uCA0BE", "debugId": null}}, {"offset": {"line": 1059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/ui/Badge.js"], "sourcesContent": ["import React from 'react';\n\nconst Badge = ({ \n  children, \n  variant = 'default', \n  size = 'default',\n  outline = false,\n  className = '',\n  ...props \n}) => {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full';\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800',\n    primary: 'bg-blue-100 text-blue-800',\n    secondary: 'bg-gray-100 text-gray-800',\n    admin: 'bg-purple-100 text-purple-800',\n    success: 'bg-green-100 text-green-800',\n    warning: 'bg-yellow-100 text-yellow-800',\n    danger: 'bg-red-100 text-red-800',\n    info: 'bg-blue-100 text-blue-800',\n    // Status badges\n    pending: 'bg-orange-100 text-orange-800',\n    completed: 'bg-green-100 text-green-800',\n    refunded: 'bg-red-100 text-red-800',\n    cancelled: 'bg-gray-100 text-gray-800',\n    // Flight status badges\n    onTime: 'bg-green-100 text-green-800',\n    delayed: 'bg-red-100 text-red-800',\n    boarding: 'bg-blue-100 text-blue-800',\n    departed: 'bg-purple-100 text-purple-800',\n  };\n\n  const outlineVariants = {\n    default: 'border border-gray-300 text-gray-700 bg-white',\n    primary: 'border border-blue-300 text-blue-700 bg-white',\n    secondary: 'border border-gray-300 text-gray-700 bg-white',\n    admin: 'border border-purple-300 text-purple-700 bg-white',\n    success: 'border border-green-300 text-green-700 bg-white',\n    warning: 'border border-yellow-300 text-yellow-700 bg-white',\n    danger: 'border border-red-300 text-red-700 bg-white',\n    info: 'border border-blue-300 text-blue-700 bg-white',\n  };\n\n  const sizes = {\n    'extra-small': 'px-2 py-0.5 text-xs',\n    small: 'px-2.5 py-0.5 text-xs',\n    default: 'px-3 py-1 text-sm',\n    large: 'px-4 py-1 text-base',\n  };\n\n  const variantClasses = outline ? outlineVariants[variant] : variants[variant];\n  \n  const badgeClasses = `\n    ${baseClasses}\n    ${variantClasses || variants.default}\n    ${sizes[size] || sizes.default}\n    ${className}\n  `.trim().replace(/\\s+/g, ' ');\n\n  return (\n    <span className={badgeClasses} {...props}>\n      {children}\n    </span>\n  );\n};\n\n// Status indicator with dot\nexport const StatusBadge = ({ status, children, ...props }) => {\n  const statusColors = {\n    onTime: 'bg-green-500',\n    delayed: 'bg-red-500',\n    boarding: 'bg-blue-500',\n    departed: 'bg-purple-500',\n    cancelled: 'bg-gray-500',\n  };\n\n  return (\n    <Badge variant={status} {...props}>\n      <span className={`w-2 h-2 rounded-full mr-2 ${statusColors[status] || 'bg-gray-500'}`}></span>\n      {children}\n    </Badge>\n  );\n};\n\n// Number badges (like notification counts)\nexport const NumberBadge = ({ number, max = 99, ...props }) => {\n  const displayNumber = number > max ? `${max}+` : number;\n  \n  return (\n    <Badge variant=\"primary\" size=\"small\" {...props}>\n      {displayNumber}\n    </Badge>\n  );\n};\n\n// Predefined badges based on design\nexport const DefaultBadge = (props) => (\n  <Badge variant=\"default\" {...props}>Default</Badge>\n);\n\nexport const PrimaryBadge = (props) => (\n  <Badge variant=\"primary\" {...props}>Primary</Badge>\n);\n\nexport const SecondaryBadge = (props) => (\n  <Badge variant=\"secondary\" {...props}>Secondary</Badge>\n);\n\nexport const AdminBadge = (props) => (\n  <Badge variant=\"admin\" {...props}>Admin</Badge>\n);\n\n// Status Badges\nexport const PendingBadge = (props) => (\n  <Badge variant=\"pending\" {...props}>Pending</Badge>\n);\n\nexport const CompletedBadge = (props) => (\n  <Badge variant=\"completed\" {...props}>Completed</Badge>\n);\n\nexport const RefundedBadge = (props) => (\n  <Badge variant=\"refunded\" {...props}>Refunded</Badge>\n);\n\nexport const CancelledBadge = (props) => (\n  <Badge variant=\"cancelled\" {...props}>Cancelled</Badge>\n);\n\n// Flight Status Badges\nexport const OnTimeBadge = (props) => (\n  <StatusBadge status=\"onTime\" {...props}>On Time</StatusBadge>\n);\n\nexport const DelayedBadge = (props) => (\n  <StatusBadge status=\"delayed\" {...props}>Delayed</StatusBadge>\n);\n\nexport const BoardingBadge = (props) => (\n  <StatusBadge status=\"boarding\" {...props}>Boarding</StatusBadge>\n);\n\nexport const DepartedBadge = (props) => (\n  <StatusBadge status=\"departed\" {...props}>Departed</StatusBadge>\n);\n\n// Outline badges\nexport const OutlineBadge = (props) => (\n  <Badge outline {...props}>Outline</Badge>\n);\n\nexport const OutlinePrimaryBadge = (props) => (\n  <Badge outline variant=\"primary\" {...props}>Outline Primary</Badge>\n);\n\nexport default Badge;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAEA,MAAM,QAAQ;QAAC,EACb,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,UAAU,KAAK,EACf,YAAY,EAAE,EACd,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;QACN,gBAAgB;QAChB,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,uBAAuB;QACvB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,kBAAkB;QACtB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,eAAe;QACf,OAAO;QACP,SAAS;QACT,OAAO;IACT;IAEA,MAAM,iBAAiB,UAAU,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ;IAE7E,MAAM,eAAe,AAAC,SAElB,OADA,aAAY,UAEZ,OADA,kBAAkB,SAAS,OAAO,EAAC,UAEnC,OADA,KAAK,CAAC,KAAK,IAAI,MAAM,OAAO,EAAC,UACnB,OAAV,WAAU,QACZ,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEzB,qBACE,yOAAC;QAAK,WAAW;QAAe,GAAG,KAAK;kBACrC;;;;;;AAGP;KA/DM;AAkEC,MAAM,cAAc;QAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;IACxD,MAAM,eAAe;QACnB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,qBACE,yOAAC;QAAM,SAAS;QAAS,GAAG,KAAK;;0BAC/B,yOAAC;gBAAK,WAAW,AAAC,6BAAkE,OAAtC,YAAY,CAAC,OAAO,IAAI;;;;;;YACrE;;;;;;;AAGP;MAfa;AAkBN,MAAM,cAAc;QAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,GAAG,OAAO;IACxD,MAAM,gBAAgB,SAAS,MAAM,AAAC,GAAM,OAAJ,KAAI,OAAK;IAEjD,qBACE,yOAAC;QAAM,SAAQ;QAAU,MAAK;QAAS,GAAG,KAAK;kBAC5C;;;;;;AAGP;MARa;AAWN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD3B;AAIN,MAAM,aAAa,CAAC,sBACzB,yOAAC;QAAM,SAAQ;QAAS,GAAG,KAAK;kBAAE;;;;;;MADvB;AAKN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;MADzB;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;MAD3B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAM,SAAQ;QAAY,GAAG,KAAK;kBAAE;;;;;;MAD1B;AAIN,MAAM,iBAAiB,CAAC,sBAC7B,yOAAC;QAAM,SAAQ;QAAa,GAAG,KAAK;kBAAE;;;;;;OAD3B;AAKN,MAAM,cAAc,CAAC,sBAC1B,yOAAC;QAAY,QAAO;QAAU,GAAG,KAAK;kBAAE;;;;;;OAD7B;AAIN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAY,QAAO;QAAW,GAAG,KAAK;kBAAE;;;;;;OAD9B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAY,QAAO;QAAY,GAAG,KAAK;kBAAE;;;;;;OAD/B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,yOAAC;QAAY,QAAO;QAAY,GAAG,KAAK;kBAAE;;;;;;OAD/B;AAKN,MAAM,eAAe,CAAC,sBAC3B,yOAAC;QAAM,OAAO;QAAE,GAAG,KAAK;kBAAE;;;;;;OADf;AAIN,MAAM,sBAAsB,CAAC,sBAClC,yOAAC;QAAM,OAAO;QAAC,SAAQ;QAAW,GAAG,KAAK;kBAAE;;;;;;OADjC;uCAIE", "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from './AuthProvider';\n\nconst ProtectedRoute = ({ \n  children, \n  allowedRoles = [], \n  redirectTo = '/login',\n  fallback = null \n}) => {\n  const { isAuthenticated, user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading) {\n      if (!isAuthenticated) {\n        router.push(redirectTo);\n        return;\n      }\n\n      if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {\n        // Redirect to appropriate dashboard based on user role\n        switch (user.role) {\n          case 'admin':\n            router.push('/admin');\n            break;\n          case 'staff':\n            router.push('/staff');\n            break;\n          case 'customer':\n            router.push('/customer');\n            break;\n          default:\n            router.push('/');\n        }\n        return;\n      }\n    }\n  }, [isAuthenticated, user, loading, allowedRoles, router, redirectTo]);\n\n  // Show loading state\n  if (loading) {\n    return (\n      fallback || (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading...</p>\n          </div>\n        </div>\n      )\n    );\n  }\n\n  // Don't render children if not authenticated\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  // Don't render children if user doesn't have required role\n  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {\n    return null;\n  }\n\n  return children;\n};\n\n// Higher-order component for role-based protection\nexport const withRoleProtection = (WrappedComponent, allowedRoles = []) => {\n  const ProtectedComponent = (props) => {\n    return (\n      <ProtectedRoute allowedRoles={allowedRoles}>\n        <WrappedComponent {...props} />\n      </ProtectedRoute>\n    );\n  };\n\n  ProtectedComponent.displayName = `withRoleProtection(${WrappedComponent.displayName || WrappedComponent.name})`;\n  \n  return ProtectedComponent;\n};\n\n// Specific role protection components\nexport const AdminRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['admin']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\nexport const StaffRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['staff']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\nexport const CustomerRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['customer']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\n// Multi-role protection\nexport const AdminOrStaffRoute = ({ children, ...props }) => (\n  <ProtectedRoute allowedRoles={['admin', 'staff']} {...props}>\n    {children}\n  </ProtectedRoute>\n);\n\n// Hook for checking permissions\nexport const usePermissions = () => {\n  const { user, isAuthenticated } = useAuth();\n\n  const hasRole = (role) => {\n    return isAuthenticated && user && user.role === role;\n  };\n\n  const hasAnyRole = (roles) => {\n    return isAuthenticated && user && roles.includes(user.role);\n  };\n\n  const isAdmin = () => hasRole('admin');\n  const isStaff = () => hasRole('staff');\n  const isCustomer = () => hasRole('customer');\n  const isAdminOrStaff = () => hasAnyRole(['admin', 'staff']);\n\n  return {\n    hasRole,\n    hasAnyRole,\n    isAdmin,\n    isStaff,\n    isCustomer,\n    isAdminOrStaff,\n    userRole: user?.role,\n  };\n};\n\nexport default ProtectedRoute;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;AACA;AACA;;;;AAJA;;;;AAMA,MAAM,iBAAiB;QAAC,EACtB,QAAQ,EACR,eAAe,EAAE,EACjB,aAAa,QAAQ,EACrB,WAAW,IAAI,EAChB;;IACC,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,mMAAO;IAClD,MAAM,SAAS,IAAA,8LAAS;IAExB,IAAA,sNAAS;oCAAC;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,aAAa,MAAM,GAAG,KAAK,QAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;oBACxE,uDAAuD;oBACvD,OAAQ,KAAK,IAAI;wBACf,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF,KAAK;4BACH,OAAO,IAAI,CAAC;4BACZ;wBACF;4BACE,OAAO,IAAI,CAAC;oBAChB;oBACA;gBACF;YACF;QACF;mCAAG;QAAC;QAAiB;QAAM;QAAS;QAAc;QAAQ;KAAW;IAErE,qBAAqB;IACrB,IAAI,SAAS;QACX,OACE,0BACE,yOAAC;YAAI,WAAU;sBACb,cAAA,yOAAC;gBAAI,WAAU;;kCACb,yOAAC;wBAAI,WAAU;;;;;;kCACf,yOAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAK5C;IAEA,6CAA6C;IAC7C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,2DAA2D;IAC3D,IAAI,aAAa,MAAM,GAAG,KAAK,QAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACxE,OAAO;IACT;IAEA,OAAO;AACT;GA7DM;;QAMuC,mMAAO;QACnC,8LAAS;;;KAPpB;AAgEC,MAAM,qBAAqB,SAAC;QAAkB,gFAAe,EAAE;IACpE,MAAM,qBAAqB,CAAC;QAC1B,qBACE,yOAAC;YAAe,cAAc;sBAC5B,cAAA,yOAAC;gBAAkB,GAAG,KAAK;;;;;;;;;;;IAGjC;IAEA,mBAAmB,WAAW,GAAG,AAAC,sBAA2E,OAAtD,iBAAiB,WAAW,IAAI,iBAAiB,IAAI,EAAC;IAE7G,OAAO;AACT;AAGO,MAAM,aAAa;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAC/C,yOAAC;QAAe,cAAc;YAAC;SAAQ;QAAG,GAAG,KAAK;kBAC/C;;;;;;;MAFQ;AAMN,MAAM,aAAa;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAC/C,yOAAC;QAAe,cAAc;YAAC;SAAQ;QAAG,GAAG,KAAK;kBAC/C;;;;;;;MAFQ;AAMN,MAAM,gBAAgB;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClD,yOAAC;QAAe,cAAc;YAAC;SAAW;QAAG,GAAG,KAAK;kBAClD;;;;;;;MAFQ;AAON,MAAM,oBAAoB;QAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACtD,yOAAC;QAAe,cAAc;YAAC;YAAS;SAAQ;QAAG,GAAG,KAAK;kBACxD;;;;;;;MAFQ;AAON,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,IAAA,mMAAO;IAEzC,MAAM,UAAU,CAAC;QACf,OAAO,mBAAmB,QAAQ,KAAK,IAAI,KAAK;IAClD;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,mBAAmB,QAAQ,MAAM,QAAQ,CAAC,KAAK,IAAI;IAC5D;IAEA,MAAM,UAAU,IAAM,QAAQ;IAC9B,MAAM,UAAU,IAAM,QAAQ;IAC9B,MAAM,aAAa,IAAM,QAAQ;IACjC,MAAM,iBAAiB,IAAM,WAAW;YAAC;YAAS;SAAQ;IAE1D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ,EAAE,iBAAA,2BAAA,KAAM,IAAI;IACtB;AACF;IAzBa;;QACuB,mMAAO;;;uCA0B5B", "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/VS%20CODE/gym/frontend/src/app/trainer/page.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Layout from '@/components/common/Layout';\nimport Button from '@/components/ui/Button';\nimport Badge from '@/components/ui/Badge';\nimport { useAuth } from '@/components/auth/AuthProvider';\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\n\nfunction TrainerDashboardContent() {\n  const { user } = useAuth();\n  const [clients, setClients] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(null);\n  const [selectedClient, setSelectedClient] = useState(null);\n\n  useEffect(() => {\n    if (user) {\n      loadTrainerData(user.id);\n    }\n  }, [user]);\n\n  const loadTrainerData = async (trainerId) => {\n    try {\n      // Load trainer's clients\n      const clientsResponse = await fetch(`/api/subscriptions?trainer_id=${trainerId}`);\n      if (clientsResponse.ok) {\n        const clientData = await clientsResponse.json();\n        setClients(clientData);\n      }\n    } catch (error) {\n      console.error('Error loading trainer data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClientAction = (action, client) => {\n    setSelectedClient(client);\n    setShowModal(action);\n  };\n\n  const closeModal = () => {\n    setShowModal(null);\n    setSelectedClient(null);\n  };\n\n  const renderModal = () => {\n    if (!showModal || !selectedClient) return null;\n\n    const modalContent = {\n      progress: {\n        title: `${selectedClient.user_name || `Client ${selectedClient.user_id}`} - Progress Report`,\n        content: (\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"p-4 bg-blue-50 rounded-lg\">\n                <h4 className=\"font-medium text-blue-800\">Weight Progress</h4>\n                <p className=\"text-2xl font-bold text-blue-900\">-8 lbs</p>\n                <p className=\"text-sm text-blue-600\">Starting: 180 lbs → Current: 172 lbs</p>\n              </div>\n              <div className=\"p-4 bg-green-50 rounded-lg\">\n                <h4 className=\"font-medium text-green-800\">Sessions Completed</h4>\n                <p className=\"text-2xl font-bold text-green-900\">12</p>\n                <p className=\"text-sm text-green-600\">This month</p>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <h4 className=\"font-medium\">Recent Achievements</h4>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center p-2 bg-yellow-50 rounded\">\n                  <svg className=\"w-5 h-5 text-yellow-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                  <span className=\"text-sm\">Increased bench press by 20 lbs</span>\n                </div>\n                <div className=\"flex items-center p-2 bg-green-50 rounded\">\n                  <svg className=\"w-5 h-5 text-green-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <span className=\"text-sm\">Completed 30-day consistency challenge</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      },\n      schedule: {\n        title: `Schedule Session - ${selectedClient.user_name || `Client ${selectedClient.user_id}`}`,\n        content: (\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Date</label>\n                <input\n                  type=\"date\"\n                  className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  defaultValue={new Date().toISOString().split('T')[0]}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Time</label>\n                <select className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\">\n                  <option>9:00 AM</option>\n                  <option>10:00 AM</option>\n                  <option>11:00 AM</option>\n                  <option>2:00 PM</option>\n                  <option>3:00 PM</option>\n                  <option>4:00 PM</option>\n                </select>\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Session Type</label>\n              <select className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\">\n                <option>Strength Training</option>\n                <option>Cardio Workout</option>\n                <option>Flexibility & Mobility</option>\n                <option>HIIT Training</option>\n                <option>Assessment Session</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Notes</label>\n              <textarea\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                rows=\"3\"\n                placeholder=\"Add any specific notes or goals for this session...\"\n              ></textarea>\n            </div>\n          </div>\n        )\n      },\n      message: {\n        title: `Message ${selectedClient.user_name || `Client ${selectedClient.user_id}`}`,\n        content: (\n          <div className=\"space-y-4\">\n            <div className=\"bg-gray-50 rounded-lg p-4 max-h-60 overflow-y-auto\">\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-end\">\n                  <div className=\"bg-blue-500 text-white rounded-lg px-3 py-2 max-w-xs\">\n                    <p className=\"text-sm\">Great job on today's workout! Keep up the excellent progress.</p>\n                    <p className=\"text-xs opacity-75 mt-1\">2 hours ago</p>\n                  </div>\n                </div>\n                <div className=\"flex justify-start\">\n                  <div className=\"bg-white border rounded-lg px-3 py-2 max-w-xs\">\n                    <p className=\"text-sm\">Thank you! I'm feeling stronger already. When is our next session?</p>\n                    <p className=\"text-xs text-gray-500 mt-1\">1 hour ago</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Send Message</label>\n              <textarea\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                rows=\"3\"\n                placeholder=\"Type your message here...\"\n              ></textarea>\n            </div>\n          </div>\n        )\n      }\n    };\n\n    const content = modalContent[showModal];\n\n    return (\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n        <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto\">\n          <div className=\"p-6 border-b border-gray-200 flex items-center justify-between\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">{content.title}</h2>\n            <button\n              onClick={closeModal}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n          <div className=\"p-6\">\n            {content.content}\n          </div>\n          <div className=\"p-6 border-t border-gray-200 flex justify-end space-x-3\">\n            <Button onClick={closeModal} variant=\"outline\">\n              Cancel\n            </Button>\n            {showModal === 'schedule' && (\n              <Button onClick={() => {\n                alert('Session scheduled successfully!');\n                closeModal();\n              }}>\n                Schedule Session\n              </Button>\n            )}\n            {showModal === 'message' && (\n              <Button onClick={() => {\n                alert('Message sent successfully!');\n                closeModal();\n              }}>\n                Send Message\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading trainer dashboard...</p>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">\n              Welcome, {user?.full_name || user?.username}!\n            </h1>\n            <p className=\"text-gray-600 mt-2\">\n              Manage your clients and track their fitness progress.\n            </p>\n          </div>\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-blue-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Active Clients</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">{clients.length}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-green-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Sessions Today</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">3</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">This Week</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">12</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-2 bg-purple-100 rounded-lg\">\n                  <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\" />\n                  </svg>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">Rating</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">4.9</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Left Column - Clients */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white rounded-lg shadow\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">My Clients</h2>\n                  <p className=\"text-gray-600 mt-1\">Manage your assigned clients and their progress</p>\n                </div>\n                <div className=\"p-6\">\n                  {clients.length === 0 ? (\n                    <div className=\"text-center py-8\">\n                      <svg className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                      </svg>\n                      <p className=\"text-gray-500\">No clients assigned yet</p>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-4\">\n                      {clients.map((client) => (\n                        <div key={client.id} className=\"border border-gray-200 rounded-lg p-4\">\n                          <div className=\"flex items-center justify-between mb-3\">\n                            <div>\n                              <h3 className=\"font-medium text-gray-900\">\n                                {client.user_name || `Client ID: ${client.user_id}`}\n                              </h3>\n                              <p className=\"text-sm text-gray-600\">\n                                Plan: {client.plan_name || client.plan_id}\n                              </p>\n                              <p className=\"text-sm text-gray-600\">\n                                Started: {new Date(client.subscribed_at).toLocaleDateString()}\n                              </p>\n                              {client.next_session && (\n                                <p className=\"text-xs text-blue-600\">\n                                  Next: {new Date(client.next_session).toLocaleString()}\n                                </p>\n                              )}\n                            </div>\n                            <Badge variant=\"success\">Active</Badge>\n                          </div>\n\n                          <div className=\"flex items-center space-x-3\">\n                            <Button\n                              size=\"small\"\n                              variant=\"outline\"\n                              onClick={() => handleClientAction('progress', client)}\n                              className=\"hover:bg-blue-50 hover:border-blue-300\"\n                            >\n                              View Progress\n                            </Button>\n                            <Button\n                              size=\"small\"\n                              variant=\"outline\"\n                              onClick={() => handleClientAction('schedule', client)}\n                              className=\"hover:bg-green-50 hover:border-green-300\"\n                            >\n                              Schedule Session\n                            </Button>\n                            <Button\n                              size=\"small\"\n                              variant=\"outline\"\n                              onClick={() => handleClientAction('message', client)}\n                              className=\"hover:bg-purple-50 hover:border-purple-300\"\n                            >\n                              Message Client\n                            </Button>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column */}\n            <div className=\"space-y-8\">\n              {/* Today's Schedule */}\n              <div className=\"bg-white rounded-lg shadow\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">Today's Schedule</h2>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">Morning Session</p>\n                        <p className=\"text-sm text-gray-600\">9:00 AM - 10:00 AM</p>\n                      </div>\n                      <Badge variant=\"primary\" size=\"small\">Upcoming</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">Afternoon Session</p>\n                        <p className=\"text-sm text-gray-600\">2:00 PM - 3:00 PM</p>\n                      </div>\n                      <Badge variant=\"secondary\" size=\"small\">Scheduled</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium text-gray-900\">Evening Session</p>\n                        <p className=\"text-sm text-gray-600\">6:00 PM - 7:00 PM</p>\n                      </div>\n                      <Badge variant=\"secondary\" size=\"small\">Scheduled</Badge>\n                    </div>\n                  </div>\n                  <Button fullWidth variant=\"outline\" size=\"small\" className=\"mt-4\">\n                    View Full Schedule\n                  </Button>\n                </div>\n              </div>\n\n              {/* Quick Actions */}\n              <div className=\"bg-white rounded-lg shadow\">\n                <div className=\"p-6 border-b border-gray-200\">\n                  <h2 className=\"text-xl font-semibold text-gray-900\">Quick Actions</h2>\n                </div>\n                <div className=\"p-6\">\n                  <div className=\"space-y-3\">\n                    <Button\n                      fullWidth\n                      variant=\"outline\"\n                      onClick={() => alert('Workout Plan Creator\\n\\nThis feature allows you to create custom workout plans for your clients. You can:\\n\\n• Select exercises from our database\\n• Set reps, sets, and weights\\n• Add progression notes\\n• Schedule workout days\\n\\nComing soon!')}\n                    >\n                      Create Workout Plan\n                    </Button>\n                    <Button\n                      fullWidth\n                      variant=\"outline\"\n                      onClick={() => alert('Diet Plan Creator\\n\\nThis feature allows you to create personalized nutrition plans. You can:\\n\\n• Set calorie targets\\n• Plan meals and snacks\\n• Track macronutrients\\n• Add dietary restrictions\\n\\nComing soon!')}\n                    >\n                      Create Diet Plan\n                    </Button>\n                    <Button\n                      fullWidth\n                      variant=\"outline\"\n                      onClick={() => alert('Quick Schedule\\n\\nUse this to quickly schedule sessions with any of your clients. You can:\\n\\n• View available time slots\\n• Send session invitations\\n• Set recurring sessions\\n• Add session notes\\n\\nComing soon!')}\n                    >\n                      Schedule Session\n                    </Button>\n                    <Button\n                      fullWidth\n                      variant=\"outline\"\n                      onClick={() => alert('Analytics Dashboard\\n\\nView detailed analytics about your training performance:\\n\\n• Client progress metrics\\n• Session completion rates\\n• Revenue tracking\\n• Performance trends\\n\\nComing soon!')}\n                    >\n                      View Analytics\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Render Modal */}\n      {renderModal()}\n    </Layout>\n  );\n}\n\nexport default function TrainerDashboard() {\n  return (\n    <ProtectedRoute allowedRoles={['trainer']}>\n      <TrainerDashboardContent />\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,SAAS;;IACP,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,mMAAO;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qNAAQ,EAAC,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,qNAAQ,EAAC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,qNAAQ,EAAC;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,qNAAQ,EAAC;IAErD,IAAA,sNAAS;6CAAC;YACR,IAAI,MAAM;gBACR,gBAAgB,KAAK,EAAE;YACzB;QACF;4CAAG;QAAC;KAAK;IAET,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,yBAAyB;YACzB,MAAM,kBAAkB,MAAM,MAAM,AAAC,iCAA0C,OAAV;YACrE,IAAI,gBAAgB,EAAE,EAAE;gBACtB,MAAM,aAAa,MAAM,gBAAgB,IAAI;gBAC7C,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAQ;QAClC,kBAAkB;QAClB,aAAa;IACf;IAEA,MAAM,aAAa;QACjB,aAAa;QACb,kBAAkB;IACpB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,CAAC,gBAAgB,OAAO;QAE1C,MAAM,eAAe;YACnB,UAAU;gBACR,OAAO,AAAC,GAAiE,OAA/D,eAAe,SAAS,IAAI,AAAC,UAAgC,OAAvB,eAAe,OAAO,GAAG;gBACzE,uBACE,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,yOAAC;4CAAE,WAAU;sDAAmC;;;;;;sDAChD,yOAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,yOAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,yOAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAG1C,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,yOAAC;oCAAI,WAAU;;sDACb,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;oDAA+B,MAAK;oDAAe,SAAQ;8DACxE,cAAA,yOAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,yOAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;oDAA8B,MAAK;oDAAe,SAAQ;8DACvE,cAAA,yOAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAwI,UAAS;;;;;;;;;;;8DAE9K,yOAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMtC;YACA,UAAU;gBACR,OAAO,AAAC,sBAAoF,OAA/D,eAAe,SAAS,IAAI,AAAC,UAAgC,OAAvB,eAAe,OAAO;gBACzF,uBACE,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;;sDACC,yOAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,yOAAC;4CACC,MAAK;4CACL,WAAU;4CACV,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;8CAGxD,yOAAC;;sDACC,yOAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,yOAAC;4CAAO,WAAU;;8DAChB,yOAAC;8DAAO;;;;;;8DACR,yOAAC;8DAAO;;;;;;8DACR,yOAAC;8DAAO;;;;;;8DACR,yOAAC;8DAAO;;;;;;8DACR,yOAAC;8DAAO;;;;;;8DACR,yOAAC;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAId,yOAAC;;8CACC,yOAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,yOAAC;oCAAO,WAAU;;sDAChB,yOAAC;sDAAO;;;;;;sDACR,yOAAC;sDAAO;;;;;;sDACR,yOAAC;sDAAO;;;;;;sDACR,yOAAC;sDAAO;;;;;;sDACR,yOAAC;sDAAO;;;;;;;;;;;;;;;;;;sCAGZ,yOAAC;;8CACC,yOAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,yOAAC;oCACC,WAAU;oCACV,MAAK;oCACL,aAAY;;;;;;;;;;;;;;;;;;YAKtB;YACA,SAAS;gBACP,OAAO,AAAC,WAAyE,OAA/D,eAAe,SAAS,IAAI,AAAC,UAAgC,OAAvB,eAAe,OAAO;gBAC9E,uBACE,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;sCACb,cAAA,yOAAC;gCAAI,WAAU;;kDACb,yOAAC;wCAAI,WAAU;kDACb,cAAA,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,yOAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;kDAG3C,yOAAC;wCAAI,WAAU;kDACb,cAAA,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,yOAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKlD,yOAAC;;8CACC,yOAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,yOAAC;oCACC,WAAU;oCACV,MAAK;oCACL,aAAY;;;;;;;;;;;;;;;;;;YAKtB;QACF;QAEA,MAAM,UAAU,YAAY,CAAC,UAAU;QAEvC,qBACE,yOAAC;YAAI,WAAU;sBACb,cAAA,yOAAC;gBAAI,WAAU;;kCACb,yOAAC;wBAAI,WAAU;;0CACb,yOAAC;gCAAG,WAAU;0CAAuC,QAAQ,KAAK;;;;;;0CAClE,yOAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,yOAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,yOAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAI3E,yOAAC;wBAAI,WAAU;kCACZ,QAAQ,OAAO;;;;;;kCAElB,yOAAC;wBAAI,WAAU;;0CACb,yOAAC,2LAAM;gCAAC,SAAS;gCAAY,SAAQ;0CAAU;;;;;;4BAG9C,cAAc,4BACb,yOAAC,2LAAM;gCAAC,SAAS;oCACf,MAAM;oCACN;gCACF;0CAAG;;;;;;4BAIJ,cAAc,2BACb,yOAAC,2LAAM;gCAAC,SAAS;oCACf,MAAM;oCACN;gCACF;0CAAG;;;;;;;;;;;;;;;;;;;;;;;IAQf;IAEA,IAAI,SAAS;QACX,qBACE,yOAAC,+LAAM;sBACL,cAAA,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCACb,yOAAC;4BAAI,WAAU;;;;;;sCACf,yOAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,qBACE,yOAAC,+LAAM;;0BACL,yOAAC;gBAAI,WAAU;0BACb,cAAA,yOAAC;oBAAI,WAAU;;sCAEb,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAG,WAAU;;wCAAmC;wCACrC,CAAA,iBAAA,2BAAA,KAAM,SAAS,MAAI,iBAAA,2BAAA,KAAM,QAAQ;wCAAC;;;;;;;8CAE9C,yOAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAMpC,yOAAC;4BAAI,WAAU;;8CACb,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,yOAAC;wDAAE,WAAU;kEAAwC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAKzE,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAChF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,yOAAC;wDAAE,WAAU;kEAAuC;;;;;;;;;;;;;;;;;;;;;;;8CAK1D,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,yOAAC;wDAAE,WAAU;kEAAuC;;;;;;;;;;;;;;;;;;;;;;;8CAK1D,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;0DACb,cAAA,yOAAC;oDAAI,WAAU;oDAA0B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjF,cAAA,yOAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,yOAAC;wDAAE,WAAU;kEAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO5D,yOAAC;4BAAI,WAAU;;8CAEb,yOAAC;oCAAI,WAAU;8CACb,cAAA,yOAAC;wCAAI,WAAU;;0DACb,yOAAC;gDAAI,WAAU;;kEACb,yOAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,yOAAC;wDAAE,WAAU;kEAAqB;;;;;;;;;;;;0DAEpC,yOAAC;gDAAI,WAAU;0DACZ,QAAQ,MAAM,KAAK,kBAClB,yOAAC;oDAAI,WAAU;;sEACb,yOAAC;4DAAI,WAAU;4DAAuC,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC9F,cAAA,yOAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,yOAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;yEAG/B,yOAAC;oDAAI,WAAU;8DACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,yOAAC;4DAAoB,WAAU;;8EAC7B,yOAAC;oEAAI,WAAU;;sFACb,yOAAC;;8FACC,yOAAC;oFAAG,WAAU;8FACX,OAAO,SAAS,IAAI,AAAC,cAA4B,OAAf,OAAO,OAAO;;;;;;8FAEnD,yOAAC;oFAAE,WAAU;;wFAAwB;wFAC5B,OAAO,SAAS,IAAI,OAAO,OAAO;;;;;;;8FAE3C,yOAAC;oFAAE,WAAU;;wFAAwB;wFACzB,IAAI,KAAK,OAAO,aAAa,EAAE,kBAAkB;;;;;;;gFAE5D,OAAO,YAAY,kBAClB,yOAAC;oFAAE,WAAU;;wFAAwB;wFAC5B,IAAI,KAAK,OAAO,YAAY,EAAE,cAAc;;;;;;;;;;;;;sFAIzD,yOAAC,0LAAK;4EAAC,SAAQ;sFAAU;;;;;;;;;;;;8EAG3B,yOAAC;oEAAI,WAAU;;sFACb,yOAAC,2LAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,mBAAmB,YAAY;4EAC9C,WAAU;sFACX;;;;;;sFAGD,yOAAC,2LAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,mBAAmB,YAAY;4EAC9C,WAAU;sFACX;;;;;;sFAGD,yOAAC,2LAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,mBAAmB,WAAW;4EAC7C,WAAU;sFACX;;;;;;;;;;;;;2DA3CK,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;8CAwD/B,yOAAC;oCAAI,WAAU;;sDAEb,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;8DACb,cAAA,yOAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;8DAEtD,yOAAC;oDAAI,WAAU;;sEACb,yOAAC;4DAAI,WAAU;;8EACb,yOAAC;oEAAI,WAAU;;sFACb,yOAAC;;8FACC,yOAAC;oFAAE,WAAU;8FAA4B;;;;;;8FACzC,yOAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;sFAEvC,yOAAC,0LAAK;4EAAC,SAAQ;4EAAU,MAAK;sFAAQ;;;;;;;;;;;;8EAExC,yOAAC;oEAAI,WAAU;;sFACb,yOAAC;;8FACC,yOAAC;oFAAE,WAAU;8FAA4B;;;;;;8FACzC,yOAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;sFAEvC,yOAAC,0LAAK;4EAAC,SAAQ;4EAAY,MAAK;sFAAQ;;;;;;;;;;;;8EAE1C,yOAAC;oEAAI,WAAU;;sFACb,yOAAC;;8FACC,yOAAC;oFAAE,WAAU;8FAA4B;;;;;;8FACzC,yOAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;sFAEvC,yOAAC,0LAAK;4EAAC,SAAQ;4EAAY,MAAK;sFAAQ;;;;;;;;;;;;;;;;;;sEAG5C,yOAAC,2LAAM;4DAAC,SAAS;4DAAC,SAAQ;4DAAU,MAAK;4DAAQ,WAAU;sEAAO;;;;;;;;;;;;;;;;;;sDAOtE,yOAAC;4CAAI,WAAU;;8DACb,yOAAC;oDAAI,WAAU;8DACb,cAAA,yOAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;8DAEtD,yOAAC;oDAAI,WAAU;8DACb,cAAA,yOAAC;wDAAI,WAAU;;0EACb,yOAAC,2LAAM;gEACL,SAAS;gEACT,SAAQ;gEACR,SAAS,IAAM,MAAM;0EACtB;;;;;;0EAGD,yOAAC,2LAAM;gEACL,SAAS;gEACT,SAAQ;gEACR,SAAS,IAAM,MAAM;0EACtB;;;;;;0EAGD,yOAAC,2LAAM;gEACL,SAAS;gEACT,SAAQ;gEACR,SAAS,IAAM,MAAM;0EACtB;;;;;;0EAGD,yOAAC,2LAAM;gEACL,SAAS;gEACT,SAAQ;gEACR,SAAS,IAAM,MAAM;0EACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYd;;;;;;;AAGP;GA/bS;;QACU,mMAAO;;;KADjB;AAicM,SAAS;IACtB,qBACE,yOAAC,qMAAc;QAAC,cAAc;YAAC;SAAU;kBACvC,cAAA,yOAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}